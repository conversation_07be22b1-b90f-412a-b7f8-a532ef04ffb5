using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using Alpaca.Markets;
using SmaTrendFollower.Models;
using StackExchange.Redis;

namespace SmaTrendFollower.Tests.TestHelpers;

/// <summary>
/// Service configuration optimized for fast test execution
/// Uses mocks for network-dependent services while keeping real business logic
/// </summary>
public static class FastTestServiceConfiguration
{
    /// <summary>
    /// Configure services for fast integration testing
    /// </summary>
    public static IServiceCollection AddFastTestServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Add basic infrastructure
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning)); // Reduce log noise

        // Add core infrastructure (Redis, etc.)
        var mockConnectionMultiplexer = new Mock<IConnectionMultiplexer>();
        var mockDatabase = new Mock<IDatabase>();
        mockConnectionMultiplexer.Setup(x => x.GetDatabase(It.IsAny<int>(), It.IsAny<object>())).Returns(mockDatabase.Object);
        mockConnectionMultiplexer.Setup(x => x.IsConnected).Returns(true);
        services.AddSingleton(mockConnectionMultiplexer.Object);

        services.AddSingleton<IRedisConnection, RedisConnectionService>();
        services.AddSingleton<IOptimizedRedisConnectionService, OptimizedRedisConnectionService>();

        // Add fast mock implementations for network-dependent services
        services.AddFastMockDataServices();
        services.AddFastMockMarketDataServices();
        services.AddFastMockTradingServices();

        // Add real Phase 6 services (business logic) but with mocked dependencies
        services.AddPhase6ServicesWithMocks();

        return services;
    }

    /// <summary>
    /// Add mock data services that don't make real network calls
    /// </summary>
    private static IServiceCollection AddFastMockDataServices(this IServiceCollection services)
    {
        // Mock TickStreamService - the main culprit for slow tests
        services.AddSingleton<ITickStreamService, MockTickStreamService>();

        // Mock other network-dependent services
        var mockPolygonClient = new Mock<IPolygonWebSocketClient>();
        mockPolygonClient.Setup(x => x.SubscribeToTradeUpdatesAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()))
                        .Returns(Task.CompletedTask);
        mockPolygonClient.Setup(x => x.SubscribeToQuoteUpdatesAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()))
                        .Returns(Task.CompletedTask);
        mockPolygonClient.Setup(x => x.SubscribeToAggregateUpdatesAsync(It.IsAny<IEnumerable<string>>(), It.IsAny<CancellationToken>()))
                        .Returns(Task.CompletedTask);
        services.AddSingleton(mockPolygonClient.Object);

        var mockStreamingService = new Mock<IStreamingDataService>();
        mockStreamingService.Setup(x => x.ConnectAlpacaStreamAsync()).Returns(Task.CompletedTask);
        mockStreamingService.Setup(x => x.ConnectPolygonStreamAsync()).Returns(Task.CompletedTask);
        mockStreamingService.Setup(x => x.SubscribeToQuotesAsync(It.IsAny<IEnumerable<string>>())).Returns(Task.CompletedTask);
        mockStreamingService.Setup(x => x.SubscribeToBarsAsync(It.IsAny<IEnumerable<string>>())).Returns(Task.CompletedTask);
        services.AddSingleton(mockStreamingService.Object);

        return services;
    }

    /// <summary>
    /// Add mock market data services
    /// </summary>
    private static IServiceCollection AddFastMockMarketDataServices(this IServiceCollection services)
    {
        // Mock market data service that returns fast mock data
        var mockMarketDataService = new Mock<IMarketDataService>();
        mockMarketDataService.Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                            .ReturnsAsync(CreateMockBarPage());
        services.AddSingleton(mockMarketDataService.Object);

        // Mock universe provider
        var mockUniverseProvider = new Mock<IUniverseProvider>();
        mockUniverseProvider.Setup(x => x.GetSymbolsAsync())
                           .ReturnsAsync(new[] { "AAPL", "MSFT", "GOOGL", "TSLA", "NVDA" });
        services.AddSingleton(mockUniverseProvider.Object);

        return services;
    }

    /// <summary>
    /// Add mock trading services
    /// </summary>
    private static IServiceCollection AddFastMockTradingServices(this IServiceCollection services)
    {
        // Mock Alpaca trading client
        var mockTradingClient = new Mock<IAlpacaTradingClient>();
        services.AddSingleton(mockTradingClient.Object);

        // Mock market regime service
        var mockRegimeService = new Mock<IMarketRegimeService>();
        mockRegimeService.Setup(x => x.IsTradingAllowedAsync(It.IsAny<CancellationToken>())).ReturnsAsync(true);
        mockRegimeService.Setup(x => x.GetCachedRegimeAsync(It.IsAny<CancellationToken>())).ReturnsAsync(MarketRegime.TrendingUp);
        services.AddSingleton(mockRegimeService.Object);

        return services;
    }

    /// <summary>
    /// Add real Phase 6 services with mocked dependencies
    /// </summary>
    private static IServiceCollection AddPhase6ServicesWithMocks(this IServiceCollection services)
    {
        // Add real Phase 6 services - they will use the mocked ITickStreamService
        services.AddScoped<IVWAPMonitorService, VWAPMonitorService>();
        services.AddScoped<ITickVolatilityGuard, TickVolatilityGuard>();
        services.AddScoped<IRealTimeBreakoutSignal, RealTimeBreakoutSignal>();
        services.AddScoped<IMicrostructurePatternDetector, MicrostructurePatternDetector>();

        // Add configurations for Phase 6 services (using record constructors)
        services.AddSingleton(new VWAPMonitorConfig(
            RollingMinutes: 30,
            MinTradesRequired: 10,
            RequireTrendingRegime: true
        ));

        services.AddSingleton(new VolatilityGuardConfig(
            VolatilityWindowMinutes: 5,
            BaseStdDevThreshold: 3.0m,
            EnableDynamicThresholds: true
        ));

        services.AddSingleton(new BreakoutSignalConfig(
            LookbackPeriodMinutes: 60,
            MinBreakoutPercent: 0.5m,
            RequireBidSupport: true
        ));

        services.AddSingleton(new MicrostructurePatternConfig(
            TickSequenceLength: 10,
            MinConsecutiveTicks: 3,
            RequireVolumeConfirmation: true
        ));

        return services;
    }

    /// <summary>
    /// Create mock bar page for testing
    /// </summary>
    private static IPage<IBar> CreateMockBarPage()
    {
        return new MockBarPage(CreateMockBars());
    }

    /// <summary>
    /// Create mock bars for testing
    /// </summary>
    private static IEnumerable<IBar> CreateMockBars()
    {
        var bars = new List<MockBar>();
        var baseTime = DateTime.UtcNow.Date.AddDays(-30); // 30 days of data
        var basePrice = 150m;

        for (int i = 0; i < 30; i++)
        {
            var open = basePrice + (decimal)(Random.Shared.NextDouble() * 10 - 5);
            var close = open + (decimal)(Random.Shared.NextDouble() * 4 - 2);
            var high = Math.Max(open, close) + (decimal)(Random.Shared.NextDouble() * 2);
            var low = Math.Min(open, close) - (decimal)(Random.Shared.NextDouble() * 2);

            bars.Add(new MockBar(
                "MOCK",
                baseTime.AddDays(i),
                open,
                high,
                low,
                close,
                Random.Shared.Next(1000000, 10000000)
            ));

            basePrice = close; // Trend the price
        }

        return bars;
    }

    /// <summary>
    /// Simple IBar implementation for testing
    /// </summary>
    private record MockBar(string Symbol, DateTime TimeUtc, decimal Open, decimal High, decimal Low, decimal Close, decimal Volume) : IBar
    {
        public decimal Vwap => (High + Low + Close) / 3;
        public ulong TradeCount => (ulong)(Volume / 100);
    }

    /// <summary>
    /// Simple IPage implementation for testing
    /// </summary>
    private class MockBarPage : IPage<IBar>
    {
        public MockBarPage(IEnumerable<IBar> items)
        {
            Items = items.ToList();
        }

        public IReadOnlyList<IBar> Items { get; }
        public string? NextPageToken => null;
        public string Symbol => "MOCK";
    }

    /// <summary>
    /// Create test configuration with fast timeouts
    /// </summary>
    public static IConfiguration CreateFastTestConfiguration()
    {
        return new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                // Fast timeouts for testing
                ["Timeouts:Http:StandardRequest"] = "00:00:02",
                ["Timeouts:Http:QuickOperation"] = "00:00:01",
                ["Timeouts:WebSocket:Connection"] = "00:00:01",
                ["Timeouts:MarketData:StreamingData"] = "00:00:02",

                // Mock API keys
                ["Alpaca:ApiKey"] = "test-key",
                ["Alpaca:SecretKey"] = "test-secret",
                ["Alpaca:BaseUrl"] = "https://paper-api.alpaca.markets",
                ["Polygon:ApiKey"] = "test-polygon-key",
                ["Redis:ConnectionString"] = "localhost:6379",

                // Phase 6 service configurations
                ["VWAPMonitor:RollingMinutes"] = "30",
                ["VWAPMonitor:MinTradesRequired"] = "10",
                ["VWAPMonitor:RequireTrendingRegime"] = "true",
                ["VolatilityGuard:VolatilityWindowMinutes"] = "5",
                ["VolatilityGuard:BaseStdDevThreshold"] = "3.0",
                ["VolatilityGuard:EnableDynamicThresholds"] = "true",
                ["BreakoutSignal:LookbackPeriodMinutes"] = "60",
                ["BreakoutSignal:MinBreakoutPercent"] = "0.5",
                ["BreakoutSignal:RequireBidSupport"] = "true",
                ["MicrostructurePattern:TickSequenceLength"] = "10",
                ["MicrostructurePattern:MinConsecutiveTicks"] = "3",
                ["MicrostructurePattern:RequireVolumeConfirmation"] = "true"
            })
            .Build();
    }
}
