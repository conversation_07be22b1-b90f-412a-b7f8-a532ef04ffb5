{"TrainedAt": "2025-07-03T05:38:00.000Z", "TrainingDuration": "00:00:01", "TrainingSamples": 10, "TestSamples": 0, "RSquared": 0.85, "MeanAbsoluteError": 0.005, "RootMeanSquaredError": 0.008, "ModelType": "LightGBM Regression (Placeholder)", "Features": ["RankProb", "ATR_Pct", "AvgSpreadPct"], "Label": "EquityPctRisk", "Description": "Placeholder model for position sizing - replace with real trained model", "Purpose": "Determines optimal position size as percentage of equity based on signal strength, volatility, and spread", "Notes": ["This is a placeholder model created with synthetic training data", "The model uses LightGBM regression to predict position size", "Input features: RankProb (signal rank probability 0-1), ATR_Pct (ATR as % of price), AvgSpreadPct (average spread %)", "Output: EquityPctRisk (recommended position size as % of equity, typically 0.01-0.10)", "Replace with real model trained on historical position sizing performance data"], "ReplacementInstructions": {"DataSource": "Collect position sizing performance data from actual trades", "TrainingScript": "Use PositionSizerRetrainerJob or TrainPositionSizer.cs", "ExpectedAccuracy": "Target R² > 0.3 for position sizing predictions", "MinimumData": "Require at least 500 trade records before training", "ValidationMethod": "Use walk-forward analysis with out-of-sample testing"}}