{"Redis": {"ConnectionString": "192.168.1.168:6379", "UniverseCacheConnection": "192.168.1.168:6379"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "SmaTrendFollower": "Information"}}, "Alpaca": {"KeyId": "AKGBPW5HD8LVI5C6NJUJ", "SecretKey": "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM", "Environment": "live"}, "Polygon": {"ApiKey": "********************************"}, "Discord": {"BotToken": "MTM4NTA1OTI3MDMzNjMxNTQ1NA.GlZAu0.dZaZAZdW5ivUiXDBDE6yqoPI-HdGA9uh2kX8qo", "ChannelId": "1385057459814797383"}, "Gemini": {"ApiKey": "AIzaSyCh_cCKoG5cY7WeSIel65-G7HbzDQQy1qc"}, "AlpacaNews": {"KeyId": "AKGBPW5HD8LVI5C6NJUJ", "Secret": "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM"}, "Safety": {"AllowedEnvironment": "Live", "MaxDailyLoss": 500, "MaxPositions": 8, "MaxSingleTradeValue": 3000, "MinAccountEquity": 5000, "MaxPositionSizePercent": 0.12, "MaxDailyTrades": 25, "RequireConfirmation": false, "DryRunMode": false}, "Strategy": {"UniverseSize": 500, "TopNSymbols": 5, "VixThreshold": 25.0, "EnableRegimeFilter": true, "EnableVolatilityFilter": true}, "USE_DYNAMIC_UNIVERSE": true, "Options": {"EnableOptionsOverlay": false, "EnableProtectivePuts": false, "EnableCoveredCalls": false}, "VWAPMonitor": {"RollingMinutes": 30, "MinTradesRequired": 2, "MinVolumeThreshold": 100, "RequireTrendingRegime": false, "DeviationAlertThreshold": 2.0, "CacheExpiryMinutes": 5, "EnableVWAPFilter": true}, "ML": {"PositionModelPath": "SmaTrendFollower.Console/Model/position_model.zip"}, "SlippageTraining": {"ModelOutputPath": "SmaTrendFollower.Console/Model/slippage_model.zip"}, "AccountStreaming": {"RefreshIntervalSeconds": 15}, "PerformanceMonitoring": {"SystemResources": {"MonitoringIntervalSeconds": 5, "CpuWarningThreshold": 80.0, "CpuCriticalThreshold": 95.0, "MemoryWarningThreshold": 85.0, "MemoryCriticalThreshold": 95.0, "ThreadCountWarning": 100, "ThreadCountCritical": 200}, "Database": {"EnableSqliteMonitoring": true, "EnableRedisMonitoring": true, "QueryLatencyWarningMs": 1000, "QueryLatencyCriticalMs": 5000, "RedisLatencyWarningMs": 100, "RedisLatencyCriticalMs": 500}, "WebSocket": {"EnableConnectionMonitoring": true, "EnableThroughputMonitoring": true, "MinConnectionRatio": 0.8, "MaxReconnectionsPerHour": 10, "MessageLatencyWarningMs": 100, "MessageLatencyCriticalMs": 1000}, "TradingPipeline": {"EnablePipelineMonitoring": true, "EnableBottleneckDetection": true, "MaxActiveExecutions": 50, "MaxBottlenecksPer5Min": 10, "SignalLatencyWarningMs": 5000, "SignalLatencyCriticalMs": 15000, "TradeExecutionLatencyWarningMs": 2000, "TradeExecutionLatencyCriticalMs": 10000}, "Alerting": {"EnableDiscordNotifications": true, "EnableEmailNotifications": false, "CriticalAlertCooldownMinutes": 5, "WarningAlertCooldownMinutes": 15, "HealthScoreWarning": 70.0, "HealthScoreCritical": 50.0}, "LoadTesting": {"EnableAutomaticTesting": false, "TestSchedule": "0 2 * * 0", "DefaultTestDurationMinutes": 5, "MaxConcurrentOperations": 50, "EnableStressTesting": true}}}