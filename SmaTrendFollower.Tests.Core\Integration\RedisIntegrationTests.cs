using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using StackExchange.Redis;
using SmaTrendFollower.Tests.Core.TestHelpers;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Integration;

public class RedisIntegrationTests
{
    private readonly IDatabase _db = InMemoryRedis.Create();

    [Fact(Timeout = 1000)]
    public async Task Redis_Should_Handle_TTL_Correctly()
    {
        // Arrange – set key with 100ms TTL for faster testing
        await _db.StringSetAsync("temp:key", "x", expiry: System.TimeSpan.FromMilliseconds(100));

        // Act – wait a bit over 100ms
        Thread.Sleep(150);

        // Assert – key is gone
        var val = await _db.StringGetAsync("temp:key");
        val.HasValue.Should().BeFalse();
    }
}
