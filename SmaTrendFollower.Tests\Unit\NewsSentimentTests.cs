using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Xunit;
using FluentAssertions;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using StackExchange.Redis;
using Alpaca.Markets;
using System.Text.Json;

namespace SmaTrendFollower.Tests.Unit;

[Collection("EnvironmentVariableTests")]
[Trait("Category", TestCategories.Unit)]

/// <summary>
/// Unit tests for news sentiment analysis system
/// </summary>
public class NewsSentimentTests : IDisposable
{
    private readonly ConnectionMultiplexer _redisConnection;
    private readonly IDatabase _redisDatabase;
    private readonly Mock<ILogger<MomentumModelTrainer>> _mockLogger;
    private readonly Mock<IMomentumCache> _mockMomentumCache;
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly IConfiguration _configuration;

    public NewsSentimentTests()
    {
        _redisConnection = InMemoryRedis.Create();
        _redisDatabase = _redisConnection.GetDatabase();
        _mockLogger = new Mock<ILogger<MomentumModelTrainer>>();
        _mockMomentumCache = new Mock<IMomentumCache>();
        _mockMarketDataService = new Mock<IMarketDataService>();

        // Create test configuration
        var configData = new Dictionary<string, string>
        {
            ["Gemini:Endpoint"] = "https://test-gemini-endpoint.com",
            ["Gemini:ApiKeyEnv"] = "TEST_GEMINI_API_KEY",
            ["Gemini:TimeoutSec"] = "30"
        };

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        // Set test environment variable
        Environment.SetEnvironmentVariable("TEST_GEMINI_API_KEY", "test-api-key");
    }

    [Fact]
    public async Task MomentumModelTrainer_GetSentimentScoreAsync_ReturnsCorrectScore()
    {
        // Arrange
        var redisService = new Mock<IOptimizedRedisConnectionService>();
        redisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>())).ReturnsAsync(_redisDatabase);

        var trainer = new MomentumModelTrainer(
            redisService.Object,
            _mockLogger.Object,
            _mockMomentumCache.Object,
            _mockMarketDataService.Object);

        var symbol = "AAPL";
        var date = DateTime.UtcNow;
        var dateKey = date.ToString("yyyyMMdd");
        var sentimentKey = $"Sentiment:{symbol}:{dateKey}";

        // Store test sentiment data in Redis
        await _redisDatabase.HashSetAsync(sentimentKey, "latest", 0.75);
        await _redisDatabase.HashSetAsync(sentimentKey, "12345", 0.80);
        await _redisDatabase.HashSetAsync(sentimentKey, "12346", 0.70);

        // Act
        var result = await trainer.GetSentimentScoreAsync(symbol, date);

        // Assert
        result.Should().Be(0.75);
    }

    [Fact]
    public async Task MomentumModelTrainer_GetSentimentScoreAsync_ReturnsAverageWhenNoLatest()
    {
        // Arrange
        var redisService = new Mock<IOptimizedRedisConnectionService>();
        redisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>())).ReturnsAsync(_redisDatabase);

        var trainer = new MomentumModelTrainer(
            redisService.Object,
            _mockLogger.Object,
            _mockMomentumCache.Object,
            _mockMarketDataService.Object);

        var symbol = "TSLA";
        var date = DateTime.UtcNow;
        var dateKey = date.ToString("yyyyMMdd");
        var sentimentKey = $"Sentiment:{symbol}:{dateKey}";

        // Store test sentiment data without "latest" key
        await _redisDatabase.HashSetAsync(sentimentKey, "12345", 0.60);
        await _redisDatabase.HashSetAsync(sentimentKey, "12346", 0.40);

        // Act
        var result = await trainer.GetSentimentScoreAsync(symbol, date);

        // Assert
        result.Should().Be(0.50); // Average of 0.60 and 0.40
    }

    [Fact]
    public async Task MomentumModelTrainer_GetSentimentScoreAsync_ReturnsNeutralWhenNoData()
    {
        // Arrange
        var redisService = new Mock<IOptimizedRedisConnectionService>();
        redisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>())).ReturnsAsync(_redisDatabase);

        var trainer = new MomentumModelTrainer(
            redisService.Object,
            _mockLogger.Object,
            _mockMomentumCache.Object,
            _mockMarketDataService.Object);

        var symbol = "NVDA";
        var date = DateTime.UtcNow;

        // Act (no data stored in Redis)
        var result = await trainer.GetSentimentScoreAsync(symbol, date);

        // Assert
        result.Should().Be(0.0); // Neutral sentiment when no data
    }

    [Fact]
    public async Task MomentumModelTrainer_CreateFeatureVectorAsync_IncludesSentiment()
    {
        // Arrange
        var redisService = new Mock<IOptimizedRedisConnectionService>();
        redisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>())).ReturnsAsync(_redisDatabase);

        var trainer = new MomentumModelTrainer(
            redisService.Object,
            _mockLogger.Object,
            _mockMomentumCache.Object,
            _mockMarketDataService.Object);

        var symbol = "MSFT";
        var date = DateTime.UtcNow;
        var dateKey = date.ToString("yyyyMMdd");
        var sentimentKey = $"Sentiment:{symbol}:{dateKey}";

        // Store sentiment data
        await _redisDatabase.HashSetAsync(sentimentKey, "latest", 0.85);

        // Mock market data (simplified for test) - need at least 50 bars for the method to work
        var mockBars = new List<IBar>();
        for (int i = 0; i < 60; i++) // Create 60 mock bars
        {
            var mockBar = new Mock<IBar>();
            mockBar.Setup(x => x.Close).Returns(100m + i); // Increasing prices
            mockBar.Setup(x => x.Open).Returns(99m + i);
            mockBar.Setup(x => x.High).Returns(101m + i);
            mockBar.Setup(x => x.Low).Returns(98m + i);
            mockBar.Setup(x => x.Volume).Returns(1000000);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-60 + i));
            mockBars.Add(mockBar.Object);
        }

        var mockBarsPage = new Mock<IPage<IBar>>();
        mockBarsPage.Setup(x => x.Items).Returns(mockBars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(mockBarsPage.Object);

        // Act
        var result = await trainer.CreateFeatureVectorAsync(symbol, date);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().Be(3); // [RSI, MACD, Sentiment]
        
        // Since we don't have real bar data, RSI and MACD will be 0, but sentiment should be 0.85
        result[2].Should().Be(0.85f); // Sentiment feature
    }

    [Fact]
    public void GeminiClient_Constructor_ThrowsWhenApiKeyMissing()
    {
        // Arrange
        Environment.SetEnvironmentVariable("TEST_GEMINI_API_KEY", null);

        var mockHttpClient = new HttpClient();
        var mockLogger = new Mock<ILogger<GeminiClient>>();

        // Act & Assert
        var action = () => new GeminiClient(_configuration, mockHttpClient, mockLogger.Object);
        action.Should().Throw<InvalidOperationException>()
            .WithMessage("*TEST_GEMINI_API_KEY*");
    }

    [Fact]
    public async Task GeminiClient_GetSentimentAsync_ReturnsNullOnHttpError()
    {
        // Arrange
        Environment.SetEnvironmentVariable("TEST_GEMINI_API_KEY", "test-key");

        var mockHttpClient = new HttpClient();
        var mockLogger = new Mock<ILogger<GeminiClient>>();

        var client = new GeminiClient(_configuration, mockHttpClient, mockLogger.Object);

        // Act (will fail because we're using a test endpoint)
        var result = await client.GetSentimentAsync("Test headline", CancellationToken.None);

        // Assert
        result.Should().BeNull(); // Should return null on HTTP error
    }

    [Fact]
    public void Newssentiment_Configuration_IsValid()
    {
        // Simple test to verify configuration structure
        var config = _configuration;

        config["Gemini:Endpoint"].Should().NotBeNullOrEmpty();
        config["Gemini:ApiKeyEnv"].Should().NotBeNullOrEmpty();
        config["Gemini:TimeoutSec"].Should().NotBeNullOrEmpty();
    }

    public void Dispose()
    {
        _redisConnection?.Dispose();
        Environment.SetEnvironmentVariable("TEST_GEMINI_API_KEY", null);
    }
}
