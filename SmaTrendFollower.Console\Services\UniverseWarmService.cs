using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that warms the universe data at startup and schedules daily refreshes at 8:00 AM ET
/// </summary>
public sealed class UniverseWarmService : BackgroundService
{
    private readonly IUniverseFetcherService _fetch;
    private readonly ITimeProvider _timeProvider;
    private readonly ILogger<UniverseWarmService> _logger;

    public UniverseWarmService(
        IUniverseFetcherService fetch,
        ITimeProvider timeProvider,
        ILogger<UniverseWarmService> logger)
    {
        _fetch = fetch ?? throw new ArgumentNullException(nameof(fetch));
        _timeProvider = timeProvider ?? throw new ArgumentNullException(nameof(timeProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            // Warm immediately at startup
            _logger.LogInformation("Starting universe warm service - performing initial refresh");
            await _fetch.RefreshUniverseAsync(stoppingToken);
            _logger.LogInformation("Initial universe refresh completed");

            // Schedule daily refreshes at 8:00 AM ET (12:00 PM UTC)
            while (!stoppingToken.IsCancellationRequested)
            {
                var next = GetNext8AmEt();
                var delay = next - _timeProvider.UtcNow;

                if (delay > TimeSpan.Zero)
                {
                    _logger.LogInformation("Next universe refresh scheduled for {NextRefresh:yyyy-MM-dd HH:mm:ss} UTC", next);
                    await Task.Delay(delay, stoppingToken);
                }

                if (!stoppingToken.IsCancellationRequested)
                {
                    _logger.LogInformation("Performing scheduled universe refresh");
                    await _fetch.RefreshUniverseAsync(stoppingToken);
                    _logger.LogInformation("Scheduled universe refresh completed");
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Universe warm service stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in universe warm service");
            throw;
        }
    }

    private DateTime GetNext8AmEt()
    {
        var now = _timeProvider.UtcNow;
        var today = now.Date;
        
        // 8:00 AM ET = 12:00 PM UTC (standard time) or 1:00 PM UTC (daylight time)
        // For simplicity, using 12:00 PM UTC
        var todayAt8AmEt = today.AddHours(12);
        
        if (now < todayAt8AmEt)
        {
            return todayAt8AmEt; // Today's 8 AM ET
        }
        else
        {
            return today.AddDays(1).AddHours(12); // Tomorrow's 8 AM ET
        }
    }
}
