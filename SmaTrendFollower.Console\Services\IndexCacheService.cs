using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;

namespace SmaTrendFollower.Services;

/// <summary>
/// SQLite-based implementation of index bar caching service.
/// Provides efficient caching of Polygon index data to minimize API calls.
/// </summary>
public sealed class IndexCacheService : IIndexCacheService
{
    private readonly IndexCacheDbContext _dbContext;
    private readonly ILogger<IndexCacheService> _logger;

    public IndexCacheService(IndexCacheDbContext dbContext, ILogger<IndexCacheService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<IEnumerable<IndexBar>> GetCachedBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            _logger.LogDebug("Retrieving cached bars for {Symbol} from {StartDate} to {EndDate}", 
                symbol, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

            var cachedBars = await _dbContext.GetCachedBarsAsync(symbol, startDate, endDate);
            var indexBars = cachedBars.Select(cb => cb.ToIndexBar()).ToList();

            _logger.LogDebug("Found {Count} cached bars for {Symbol}", indexBars.Count, symbol);
            return indexBars;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached bars for {Symbol}", symbol);
            return Enumerable.Empty<IndexBar>();
        }
    }

    public async Task CacheBarsAsync(string symbol, IEnumerable<IndexBar> indexBars)
    {
        try
        {
            var barsList = indexBars.ToList();
            if (!barsList.Any())
            {
                _logger.LogDebug("No bars to cache for {Symbol}", symbol);
                return;
            }

            _logger.LogDebug("Caching {Count} bars for {Symbol}", barsList.Count, symbol);

            await _dbContext.AddOrUpdateCachedBarsAsync(symbol, barsList);

            _logger.LogInformation("Successfully cached {Count} bars for {Symbol}", barsList.Count, symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching bars for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<(DateTime startDate, DateTime endDate)?> GetMissingDateRangeAsync(string symbol, DateTime requestedStartDate, DateTime requestedEndDate)
    {
        try
        {
            var latestCachedDate = await GetLatestCachedDateAsync(symbol);

            // If no cached data, fetch entire range
            if (!latestCachedDate.HasValue)
            {
                _logger.LogDebug("No cached data for {Symbol}, need to fetch entire range", symbol);
                return (requestedStartDate, requestedEndDate);
            }

            // If cache is up to date, no fetch needed
            if (latestCachedDate.Value >= requestedEndDate)
            {
                _logger.LogDebug("Cache for {Symbol} is up to date (latest: {LatestDate})", 
                    symbol, latestCachedDate.Value.ToString("yyyy-MM-dd"));
                return null;
            }

            // Need to fetch from day after latest cached date
            var fetchStartDate = latestCachedDate.Value.AddDays(1);

            // Validate that fetchStartDate is not after requestedEndDate
            if (fetchStartDate > requestedEndDate)
            {
                _logger.LogDebug("Cache for {Symbol} is already up to date - latest cached date {LatestDate} is >= requested end date {RequestedEnd}",
                    symbol, latestCachedDate.Value.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"));
                return null; // No data needs to be fetched
            }

            _logger.LogDebug("Cache for {Symbol} needs update from {FetchStart} to {RequestedEnd}",
                symbol, fetchStartDate.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"));

            return (fetchStartDate, requestedEndDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining missing date range for {Symbol}", symbol);
            // On error, fetch entire range to be safe
            return (requestedStartDate, requestedEndDate);
        }
    }

    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol)
    {
        try
        {
            return await _dbContext.GetLatestCachedDateAsync(symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest cached date for {Symbol}", symbol);
            return null;
        }
    }

    public async Task<bool> IsCacheFreshAsync(string symbol, DateTime requestedEndDate)
    {
        try
        {
            var latestCachedDate = await GetLatestCachedDateAsync(symbol);
            
            if (!latestCachedDate.HasValue)
                return false;

            // Consider cache fresh if it has data up to the requested end date
            // For index data, we typically want data up to the previous trading day
            var isFresh = latestCachedDate.Value >= requestedEndDate;
            
            _logger.LogDebug("Cache freshness for {Symbol}: latest={LatestDate}, requested={RequestedDate}, fresh={IsFresh}",
                symbol, latestCachedDate.Value.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"), isFresh);
            
            return isFresh;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache freshness for {Symbol}", symbol);
            return false;
        }
    }

    public async Task PerformMaintenanceAsync(int retainDays = 365)
    {
        try
        {
            _logger.LogInformation("Starting cache maintenance, retaining {RetainDays} days of data", retainDays);

            await _dbContext.CleanupOldDataAsync(retainDays);

            _logger.LogInformation("Cache maintenance completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache maintenance");
            throw;
        }
    }

    public async Task InitializeCacheAsync()
    {
        try
        {
            _logger.LogDebug("Initializing index cache database");

            await _dbContext.EnsureDatabaseCreatedAsync();

            _logger.LogDebug("Index cache database initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing cache database");
            throw;
        }
    }
}
