using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Example showing how to integrate API rate limit monitoring with existing services
/// Demonstrates automatic monitoring of HTTP clients and API calls
/// </summary>
public class ApiRateLimitIntegrationExample
{
    private readonly ILogger<ApiRateLimitIntegrationExample> _logger;
    private readonly IApiRateLimitMonitor _rateLimitMonitor;
    private readonly IApiRateLimitWrapper _rateLimitWrapper;
    private readonly HttpClient _httpClient;

    public ApiRateLimitIntegrationExample(
        ILogger<ApiRateLimitIntegrationExample> logger,
        IApiRateLimitMonitor rateLimitMonitor,
        IApiRateLimitWrapper rateLimitWrapper,
        HttpClient httpClient)
    {
        _logger = logger;
        _rateLimitMonitor = rateLimitMonitor;
        _rateLimitWrapper = rateLimitWrapper;
        _httpClient = httpClient;
    }

    /// <summary>
    /// Example 1: Wrapping an existing HTTP client for automatic monitoring
    /// </summary>
    public async Task ExampleWrappedHttpClient()
    {
        _logger.LogInformation("Example 1: Wrapping HTTP client for automatic monitoring");

        // Wrap the HTTP client to automatically monitor all requests
        var monitoredClient = _rateLimitWrapper.WrapHttpClient(_httpClient, "Alpaca");

        try
        {
            // All requests through this client will be automatically monitored
            var response = await monitoredClient.GetAsync("https://paper-api.alpaca.markets/v2/account");
            
            _logger.LogInformation("Request completed with status: {StatusCode}", response.StatusCode);
            
            // Rate limit information is automatically extracted from headers and recorded
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "HTTP request failed");
            // Failures are automatically recorded with timing information
        }
    }

    /// <summary>
    /// Example 2: Manual monitoring of HTTP requests
    /// </summary>
    public async Task ExampleManualHttpMonitoring()
    {
        _logger.LogInformation("Example 2: Manual HTTP request monitoring");

        await _rateLimitWrapper.ExecuteRequestAsync(
            _httpClient,
            "Polygon",
            async () =>
            {
                // Your existing HTTP request code
                var request = new HttpRequestMessage(HttpMethod.Get, "https://api.polygon.io/v2/aggs/ticker/AAPL/range/1/day/2023-01-01/2023-12-31");
                request.Headers.Add("Authorization", "Bearer YOUR_API_KEY");
                
                return await _httpClient.SendAsync(request);
            },
            endpoint: "/v2/aggs/ticker/AAPL/range",
            method: "GET"
        );
    }

    /// <summary>
    /// Example 3: Monitoring non-HTTP API calls (e.g., SDK calls)
    /// </summary>
    public async Task ExampleSdkApiMonitoring()
    {
        _logger.LogInformation("Example 3: Monitoring SDK API calls");

        // Monitor Alpaca SDK calls
        var account = await _rateLimitWrapper.ExecuteApiCallAsync(
            "Alpaca",
            "GetAccount",
            async () =>
            {
                // Your existing Alpaca SDK call
                // var alpacaClient = serviceProvider.GetService<IAlpacaTradingClient>();
                // return await alpacaClient.GetAccountAsync();
                
                // Simulated for example
                await Task.Delay(100);
                return new { AccountId = "test", BuyingPower = 10000 };
            }
        );

        _logger.LogInformation("Account retrieved: {AccountId}", account.AccountId);
    }

    /// <summary>
    /// Example 4: Integrating with existing Alpaca service
    /// </summary>
    public async Task ExampleAlpacaServiceIntegration()
    {
        _logger.LogInformation("Example 4: Integrating with existing Alpaca service");

        // This shows how to modify existing services to include monitoring
        // In your existing AlpacaDataService, you would add monitoring like this:

        try
        {
            var bars = await _rateLimitWrapper.ExecuteApiCallAsync(
                "Alpaca",
                "GetBars",
                async () =>
                {
                    // Your existing Alpaca bars API call
                    // return await alpacaDataClient.GetBarsAsync(new BarsRequest("AAPL", TimeFrame.Day));
                    
                    // Simulated for example
                    await Task.Delay(200);
                    return new[] { new { Symbol = "AAPL", Close = 150.0, Timestamp = DateTime.UtcNow } };
                }
            );

            _logger.LogInformation("Retrieved {Count} bars", bars.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get bars from Alpaca");
            // Exception is automatically recorded with timing
        }
    }

    /// <summary>
    /// Example 5: Monitoring Discord webhook calls
    /// </summary>
    public async Task ExampleDiscordWebhookMonitoring()
    {
        _logger.LogInformation("Example 5: Monitoring Discord webhook calls");

        await _rateLimitWrapper.ExecuteRequestAsync(
            _httpClient,
            "Discord",
            async () =>
            {
                var webhookUrl = "https://discord.com/api/webhooks/YOUR_WEBHOOK_URL";
                var payload = new
                {
                    content = "Trading alert: Portfolio value updated",
                    embeds = new[]
                    {
                        new
                        {
                            title = "Portfolio Update",
                            description = "Current value: $10,000",
                            color = 0x00ff00
                        }
                    }
                };

                var json = System.Text.Json.JsonSerializer.Serialize(payload);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
                
                return await _httpClient.PostAsync(webhookUrl, content);
            },
            endpoint: "/api/webhooks",
            method: "POST"
        );
    }

    /// <summary>
    /// Example 6: Getting rate limit status and analysis
    /// </summary>
    public async Task ExampleRateLimitAnalysis()
    {
        _logger.LogInformation("Example 6: Getting rate limit status and analysis");

        // Get current rate limit status for all services
        var status = await _rateLimitMonitor.GetRateLimitStatusAsync();
        
        foreach (var service in status.Services)
        {
            _logger.LogInformation("Service {Service}: {Status} - {Utilization:F1}% utilization",
                service.Key, service.Value.Status, service.Value.UtilizationPercentage);
            
            if (service.Value.RateLimitRemaining.HasValue)
            {
                _logger.LogInformation("  Rate limit remaining: {Remaining}", service.Value.RateLimitRemaining);
            }
            
            if (service.Value.TotalRateLimitExceeded > 0)
            {
                _logger.LogWarning("  Rate limit exceeded {Count} times", service.Value.TotalRateLimitExceeded);
            }
        }

        // Get performance metrics
        var metrics = await _rateLimitMonitor.GetApiPerformanceMetricsAsync();
        
        foreach (var metric in metrics.ServiceMetrics)
        {
            _logger.LogInformation("Service {Service}: {TotalRequests} requests, {AvgLatency:F1}ms avg latency, {SuccessRate:P1} success rate",
                metric.Key, metric.Value.TotalRequests, metric.Value.AverageLatencyMs, metric.Value.SuccessRate);
        }

        // Get rate limit analysis and predictions
        var analysis = await _rateLimitMonitor.AnalyzeRateLimitsAsync();
        
        if (analysis.Recommendations.Any())
        {
            _logger.LogInformation("Rate limit recommendations:");
            foreach (var recommendation in analysis.Recommendations)
            {
                _logger.LogInformation("  - {Recommendation}", recommendation);
            }
        }

        foreach (var prediction in analysis.Predictions)
        {
            if (prediction.Value.TimeToRateLimit.HasValue)
            {
                _logger.LogWarning("Service {Service} may hit rate limit in {TimeToLimit}",
                    prediction.Key, prediction.Value.TimeToRateLimit.Value);
            }
        }
    }

    /// <summary>
    /// Example 7: Manual rate limit recording (for testing or custom integrations)
    /// </summary>
    public void ExampleManualRecording()
    {
        _logger.LogInformation("Example 7: Manual rate limit recording");

        // Manually record an API request (useful for testing or custom integrations)
        _rateLimitMonitor.RecordApiRequest(
            service: "CustomAPI",
            endpoint: "/api/data",
            method: "GET",
            statusCode: 200,
            duration: TimeSpan.FromMilliseconds(150),
            rateLimitRemaining: 95,
            rateLimitReset: DateTime.UtcNow.AddMinutes(1)
        );

        // Record a rate limit exceeded event
        _rateLimitMonitor.RecordRateLimitExceeded(
            service: "CustomAPI",
            endpoint: "/api/data",
            retryAfterSeconds: 60
        );

        _logger.LogInformation("Manual recordings completed");
    }

    /// <summary>
    /// Example 8: Integration with existing rate limiters
    /// </summary>
    public async Task ExampleExistingRateLimiterIntegration()
    {
        _logger.LogInformation("Example 8: Integration with existing rate limiters");

        // This shows how to integrate monitoring with existing rate limiters
        // You would modify your existing AlpacaRateLimitHelper like this:

        /*
        public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown")
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                // Your existing rate limiting logic
                await _semaphore.WaitAsync();
                await WaitIfNecessary();
                Interlocked.Increment(ref _requestCount);
                
                // Execute the API call
                var result = await apiCall();
                
                stopwatch.Stop();
                
                // Add monitoring integration
                _rateLimitMonitor?.RecordApiRequest(
                    "Alpaca",
                    operationName,
                    "API",
                    200, // Assume success
                    stopwatch.Elapsed,
                    200 - _requestCount, // Remaining requests
                    _windowStart.AddMinutes(1) // Reset time
                );
                
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                // Record failed request
                _rateLimitMonitor?.RecordApiRequest(
                    "Alpaca",
                    operationName,
                    "API",
                    500, // Error status
                    stopwatch.Elapsed
                );
                
                // Check if it's a rate limit exception
                if (IsRateLimitException(ex))
                {
                    _rateLimitMonitor?.RecordRateLimitExceeded("Alpaca", operationName);
                }
                
                throw;
            }
            finally
            {
                _semaphore.Release();
            }
        }
        */

        _logger.LogInformation("Integration pattern shown in comments above");
    }
}
