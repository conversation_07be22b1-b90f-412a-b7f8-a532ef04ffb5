using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System;
using System.Threading.Tasks;

namespace SmaTrendFollower.Tools
{
    /// <summary>
    /// Simple tool to clear Redis universe cache
    /// </summary>
    public class ClearRedisCache
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("🧹 Clearing Redis universe cache...");

            try
            {
                // Load configuration
                var configuration = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json", optional: true)
                    .AddJsonFile("appsettings.Development.json", optional: true)
                    .AddJsonFile("appsettings.LocalProd.json", optional: true)
                    .AddEnvironmentVariables()
                    .Build();

                // Get Redis connection string
                var redisUrl = configuration.GetSection("Redis")["ConnectionString"]
                              ?? configuration["REDIS_URL"]
                              ?? Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING")
                              ?? Environment.GetEnvironmentVariable("Redis__ConnectionString")
                              ?? "localhost:6379";

                if (string.IsNullOrEmpty(redisUrl))
                {
                    Console.WriteLine("❌ Redis connection string not found");
                    return;
                }

                Console.WriteLine($"📡 Connecting to Redis: {redisUrl}");

                // Connect to Redis
                var configOptions = ConfigurationOptions.Parse(redisUrl);
                configOptions.AbortOnConnectFail = false;

                var redisPassword = configuration["REDIS_PASSWORD"];
                if (!string.IsNullOrEmpty(redisPassword))
                {
                    configOptions.Password = redisPassword;
                }

                using var connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
                var database = connectionMultiplexer.GetDatabase(0);

                // Get all universe-related keys
                var server = connectionMultiplexer.GetServer(connectionMultiplexer.GetEndPoints()[0]);
                var universeKeys = server.Keys(pattern: "universe:*");
                var candidateKeys = server.Keys(pattern: "Universe:*");

                int deletedCount = 0;

                // Delete universe keys
                foreach (var key in universeKeys)
                {
                    await database.KeyDeleteAsync(key);
                    deletedCount++;
                    Console.WriteLine($"🗑️  Deleted: {key}");
                }

                // Delete candidate keys (with capital U)
                foreach (var key in candidateKeys)
                {
                    await database.KeyDeleteAsync(key);
                    deletedCount++;
                    Console.WriteLine($"🗑️  Deleted: {key}");
                }

                // Also clear any symbol list cache keys
                var symbolKeys = server.Keys(pattern: "*symbol*");
                foreach (var key in symbolKeys)
                {
                    if (key.ToString().Contains("universe", StringComparison.OrdinalIgnoreCase) ||
                        key.ToString().Contains("candidate", StringComparison.OrdinalIgnoreCase) ||
                        key.ToString().Contains("polygon", StringComparison.OrdinalIgnoreCase))
                    {
                        await database.KeyDeleteAsync(key);
                        deletedCount++;
                        Console.WriteLine($"🗑️  Deleted: {key}");
                    }
                }

                Console.WriteLine($"✅ Cleared {deletedCount} universe-related keys from Redis");
                Console.WriteLine("🔄 Universe data will be refreshed on next fetch");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error clearing Redis cache: {ex.Message}");
                Console.WriteLine($"   Details: {ex}");
            }
        }
    }
}
