using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using Alpaca.Markets;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Services;

/// <summary>
/// Comprehensive unit tests for WheelStrategyEngine
/// </summary>
public class WheelStrategyEngineTests : IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IMarketSessionGuard _sessionGuard;
    private readonly ITradeExecutor _tradeExecutor;
    private readonly IVIXResolverService _vixResolver;
    private readonly IDiscordNotificationService _discordService;
    private readonly IEarningsCalendar _earningsCalendar;
    private readonly ILogger<WheelStrategyEngine> _logger;
    private readonly IOptionsMonitor<WheelStrategyConfig> _configMonitor;
    private readonly WheelStrategyEngine _wheelEngine;
    private readonly WheelStrategyConfig _defaultConfig;

    public WheelStrategyEngineTests()
    {
        _marketDataService = Substitute.For<IMarketDataService>();
        _sessionGuard = Substitute.For<IMarketSessionGuard>();
        _tradeExecutor = Substitute.For<ITradeExecutor>();
        _vixResolver = Substitute.For<IVIXResolverService>();
        _discordService = Substitute.For<IDiscordNotificationService>();
        _earningsCalendar = Substitute.For<IEarningsCalendar>();
        _logger = Substitute.For<ILogger<WheelStrategyEngine>>();
        _configMonitor = Substitute.For<IOptionsMonitor<WheelStrategyConfig>>();

        _defaultConfig = new WheelStrategyConfig
        {
            Enabled = true,
            MaxAllocationPercent = 0.20m,
            MinPremiumPercent = 0.01m,
            MinDaysToExpiration = 7,
            MaxDaysToExpiration = 45,
            MaxDeltaForPuts = 0.30m,
            MaxDeltaForCalls = 0.30m,
            MinLiquidity = 100m,
            MaxBidAskSpreadPercent = 0.05m,
            EnableRolling = true,
            RollThreshold = 0.50m,
            MaxRollAttempts = 2,
            RequireHighIV = true,
            MinIVPercentile = 30m,
            AllowedSymbols = new[] { "SPY", "AAPL", "MSFT" },
            ExcludedSymbols = null
        };

        _configMonitor.CurrentValue.Returns(_defaultConfig);

        _wheelEngine = new WheelStrategyEngine(
            _marketDataService,
            _sessionGuard,
            _tradeExecutor,
            _vixResolver,
            _discordService,
            _earningsCalendar,
            _logger,
            _configMonitor
        );
    }

    [Fact]
    public async Task IsSymbolSuitableForWheelAsync_WithAllowedSymbol_ReturnsTrue()
    {
        // Arrange
        var symbol = "AAPL";
        var bars = CreateMockBars(149.51m);
        var optionsData = new OptionData[] { new OptionData($"{symbol}P", symbol, DateTime.UtcNow.AddDays(30), 140m, "put", 2.55m, 2.50m, 2.60m, 1000, 5000, 0.35m, -0.25m, 0.02m, -0.05m, 0.15m) };

        _marketDataService.GetStockBarsAsync(symbol, Arg.Any<DateTime>(), Arg.Any<DateTime>()).Returns(bars);
        _marketDataService.GetOptionsDataAsync(symbol).Returns(optionsData);

        // Act
        var result = await _wheelEngine.IsSymbolSuitableForWheelAsync(symbol);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsSymbolSuitableForWheelAsync_WithExcludedSymbol_ReturnsFalse()
    {
        // Arrange
        var symbol = "TSLA";
        var configWithExclusions = new WheelStrategyConfig
        {
            Enabled = _defaultConfig.Enabled,
            MaxAllocationPercent = _defaultConfig.MaxAllocationPercent,
            MinPremiumPercent = _defaultConfig.MinPremiumPercent,
            MinDaysToExpiration = _defaultConfig.MinDaysToExpiration,
            MaxDaysToExpiration = _defaultConfig.MaxDaysToExpiration,
            MaxDeltaForPuts = _defaultConfig.MaxDeltaForPuts,
            MaxDeltaForCalls = _defaultConfig.MaxDeltaForCalls,
            MinLiquidity = _defaultConfig.MinLiquidity,
            MaxBidAskSpreadPercent = _defaultConfig.MaxBidAskSpreadPercent,
            EnableRolling = _defaultConfig.EnableRolling,
            RollThreshold = _defaultConfig.RollThreshold,
            MaxRollAttempts = _defaultConfig.MaxRollAttempts,
            RequireHighIV = _defaultConfig.RequireHighIV,
            MinIVPercentile = _defaultConfig.MinIVPercentile,
            AllowedSymbols = _defaultConfig.AllowedSymbols,
            ExcludedSymbols = new[] { "TSLA" }
        };
        _configMonitor.CurrentValue.Returns(configWithExclusions);

        // Act
        var result = await _wheelEngine.IsSymbolSuitableForWheelAsync(symbol);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsSymbolSuitableForWheelAsync_WithLowPrice_ReturnsFalse()
    {
        // Arrange
        var symbol = "AAPL";
        var bars = CreateMockBars(5.01m); // Below $10 threshold

        _marketDataService.GetStockBarsAsync(symbol, Arg.Any<DateTime>(), Arg.Any<DateTime>()).Returns(bars);

        // Act
        var result = await _wheelEngine.IsSymbolSuitableForWheelAsync(symbol);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsSymbolSuitableForWheelAsync_WithNoQuote_ReturnsFalse()
    {
        // Arrange
        var symbol = "AAPL";
        _marketDataService.GetStockBarsAsync(symbol, Arg.Any<DateTime>(), Arg.Any<DateTime>()).Returns(Task.FromException<IPage<IBar>>(new InvalidOperationException("No data available")));

        // Act
        var result = await _wheelEngine.IsSymbolSuitableForWheelAsync(symbol);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task RunWheelCycleAsync_WithUnsuitableSymbol_ReturnsNoAction()
    {
        // Arrange
        var symbol = "INVALID";
        _marketDataService.GetStockBarsAsync(symbol, Arg.Any<DateTime>(), Arg.Any<DateTime>()).Returns(Task.FromException<IPage<IBar>>(new InvalidOperationException("No data available")));

        // Act
        var result = await _wheelEngine.RunWheelCycleAsync(symbol);

        // Assert
        result.Success.Should().BeFalse();
        result.Action.Should().Be(WheelAction.NoAction);
        result.Reason.Should().Be("Symbol not suitable for wheel strategy");
    }

    [Fact]
    public async Task GetCurrentPositionsAsync_WithNoPositions_ReturnsEmpty()
    {
        // Act
        var positions = await _wheelEngine.GetCurrentPositionsAsync();

        // Assert
        positions.Should().BeEmpty();
    }

    [Fact]
    public async Task GetPositionAsync_WithNonExistentSymbol_ReturnsNull()
    {
        // Act
        var position = await _wheelEngine.GetPositionAsync("NONEXISTENT");

        // Assert
        position.Should().BeNull();
    }

    [Fact]
    public async Task GetPerformanceMetricsAsync_WithNoPositions_ReturnsZeroMetrics()
    {
        // Act
        var metrics = await _wheelEngine.GetPerformanceMetricsAsync();

        // Assert
        metrics.TotalPremiumCollected.Should().Be(0);
        metrics.UnrealizedPnL.Should().Be(0);
        metrics.RealizedPnL.Should().Be(0);
        metrics.ActivePositions.Should().Be(0);
        metrics.CompletedCycles.Should().Be(0);
        metrics.AnnualizedReturn.Should().Be(0);
        metrics.WinRate.Should().Be(0);
        metrics.AveragePremiumPerCycle.Should().Be(0);
    }

    [Fact]
    public async Task UpdateConfigurationAsync_UpdatesConfiguration()
    {
        // Arrange
        var newConfig = new WheelStrategyConfig { MaxAllocationPercent = 0.15m };

        // Act
        await _wheelEngine.UpdateConfigurationAsync(newConfig);

        // Assert
        // Configuration should be updated (verified through behavior in subsequent calls)
        // This is more of an integration test, but we can verify the method completes successfully
        await Task.CompletedTask;
    }

    [Theory]
    [InlineData("SPY", true)]
    [InlineData("AAPL", true)]
    [InlineData("MSFT", true)]
    [InlineData("TSLA", false)] // Not in allowed list
    public async Task IsSymbolSuitableForWheelAsync_WithAllowedSymbolsList_ReturnsExpectedResult(string symbol, bool expected)
    {
        // Arrange
        var bars = CreateMockBars(149.51m);
        var optionsData = new OptionData[] { new OptionData($"{symbol}P", symbol, DateTime.UtcNow.AddDays(30), 140m, "put", 2.55m, 2.50m, 2.60m, 1000, 5000, 0.35m, -0.25m, 0.02m, -0.05m, 0.15m) };

        _marketDataService.GetStockBarsAsync(symbol, Arg.Any<DateTime>(), Arg.Any<DateTime>()).Returns(bars);
        _marketDataService.GetOptionsDataAsync(symbol).Returns(optionsData);

        // Act
        var result = await _wheelEngine.IsSymbolSuitableForWheelAsync(symbol);

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public async Task ManageExistingPositionsAsync_WithNoPositions_CompletesSuccessfully()
    {
        // Act
        var act = async () => await _wheelEngine.ManageExistingPositionsAsync();

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public void WheelPosition_Creation_SetsPropertiesCorrectly()
    {
        // Arrange & Act
        var position = new WheelPosition(
            Symbol: "AAPL",
            Type: WheelPositionType.CashSecuredPut,
            Quantity: 2,
            Strike: 145.00m,
            Expiration: DateTime.UtcNow.AddDays(30),
            OptionSymbol: "AAPL240315P00145000",
            Premium: 500.00m,
            UnrealizedPnL: -100.00m,
            CreatedAt: DateTime.UtcNow,
            Status: WheelPositionStatus.Active
        );

        // Assert
        position.Symbol.Should().Be("AAPL");
        position.Type.Should().Be(WheelPositionType.CashSecuredPut);
        position.Quantity.Should().Be(2);
        position.Strike.Should().Be(145.00m);
        position.Premium.Should().Be(500.00m);
        position.UnrealizedPnL.Should().Be(-100.00m);
        position.Status.Should().Be(WheelPositionStatus.Active);
    }

    [Fact]
    public void WheelCycleResult_Creation_SetsPropertiesCorrectly()
    {
        // Arrange & Act
        var result = new WheelCycleResult(
            Symbol: "AAPL",
            Action: WheelAction.SellCashSecuredPut,
            Success: true,
            Reason: "Cash-secured put sold successfully",
            Premium: 500.00m,
            OptionSymbol: "AAPL240315P00145000",
            Strike: 145.00m,
            Expiration: DateTime.UtcNow.AddDays(30),
            Quantity: 2
        );

        // Assert
        result.Symbol.Should().Be("AAPL");
        result.Action.Should().Be(WheelAction.SellCashSecuredPut);
        result.Success.Should().BeTrue();
        result.Reason.Should().Be("Cash-secured put sold successfully");
        result.Premium.Should().Be(500.00m);
        result.Strike.Should().Be(145.00m);
        result.Quantity.Should().Be(2);
    }

    [Fact]
    public void WheelStrategyConfig_DefaultValues_AreCorrect()
    {
        // Arrange & Act
        var config = new WheelStrategyConfig();

        // Assert
        config.Enabled.Should().BeTrue();
        config.MaxAllocationPercent.Should().Be(0.20m);
        config.MinPremiumPercent.Should().Be(0.01m);
        config.MinDaysToExpiration.Should().Be(7);
        config.MaxDaysToExpiration.Should().Be(45);
        config.MaxDeltaForPuts.Should().Be(0.30m);
        config.MaxDeltaForCalls.Should().Be(0.30m);
        config.EnableRolling.Should().BeTrue();
        config.RequireHighIV.Should().BeTrue();
    }

    private IPage<IBar> CreateMockBars(decimal closePrice)
    {
        var bar = Substitute.For<IBar>();
        bar.Close.Returns(closePrice);
        bar.TimeUtc.Returns(DateTime.UtcNow);

        var page = Substitute.For<IPage<IBar>>();
        page.Items.Returns(new[] { bar });

        return page;
    }

    public void Dispose()
    {
        _wheelEngine?.Dispose();
    }
}

/// <summary>
/// Integration tests for wheel strategy with realistic scenarios
/// </summary>
public class WheelStrategyIntegrationTests : IDisposable
{
    private readonly WheelStrategyEngine _wheelEngine;
    private readonly IMarketDataService _marketDataService;
    private readonly IMarketSessionGuard _sessionGuard;
    private readonly IOptionsMonitor<WheelStrategyConfig> _configMonitor;

    public WheelStrategyIntegrationTests()
    {
        _marketDataService = Substitute.For<IMarketDataService>();
        _sessionGuard = Substitute.For<IMarketSessionGuard>();
        var tradeExecutor = Substitute.For<ITradeExecutor>();
        var vixResolver = Substitute.For<IVIXResolverService>();
        var discordService = Substitute.For<IDiscordNotificationService>();
        var earningsCalendar = Substitute.For<IEarningsCalendar>();
        var logger = Substitute.For<ILogger<WheelStrategyEngine>>();
        _configMonitor = Substitute.For<IOptionsMonitor<WheelStrategyConfig>>();

        var config = new WheelStrategyConfig
        {
            Enabled = true,
            AllowedSymbols = new[] { "SPY", "AAPL" }
        };
        _configMonitor.CurrentValue.Returns(config);

        _wheelEngine = new WheelStrategyEngine(
            _marketDataService,
            _sessionGuard,
            tradeExecutor,
            vixResolver,
            discordService,
            earningsCalendar,
            logger,
            _configMonitor
        );
    }

    [Fact]
    public async Task WheelStrategy_FullCycle_WorksCorrectly()
    {
        // Arrange
        _sessionGuard.CanTradeNowAsync().Returns(true);
        var bars = CreateMockBars(149.51m);
        _marketDataService.GetStockBarsAsync("AAPL", Arg.Any<DateTime>(), Arg.Any<DateTime>()).Returns(bars);

        var account = Substitute.For<IAccount>();
        account.Equity.Returns(100000m);
        _marketDataService.GetAccountAsync().Returns(account);

        var positions = new List<IPosition>();
        _marketDataService.GetPositionsAsync().Returns(Task.FromResult<IReadOnlyList<IPosition>>(positions));

        // Act
        var result = await _wheelEngine.RunWheelCycleAsync("AAPL");

        // Assert
        result.Should().NotBeNull();
        result.Symbol.Should().Be("AAPL");
    }

    [Fact]
    public async Task WheelStrategy_WithMarketClosed_SkipsExecution()
    {
        // Arrange
        _sessionGuard.CanTradeNowAsync().Returns(false);

        // Act
        var result = await _wheelEngine.RunWheelCycleAsync("AAPL");

        // Assert
        result.Success.Should().BeFalse();
        result.Reason.Should().Be("Symbol not suitable for wheel strategy");
    }

    private IPage<IBar> CreateMockBars(decimal closePrice)
    {
        var bar = Substitute.For<IBar>();
        bar.Close.Returns(closePrice);
        bar.TimeUtc.Returns(DateTime.UtcNow);

        var page = Substitute.For<IPage<IBar>>();
        page.Items.Returns(new[] { bar });

        return page;
    }



    public void Dispose()
    {
        _wheelEngine?.Dispose();
    }
}

// Mock classes for testing
public class Quote
{
    public decimal BidPrice { get; set; }
    public decimal AskPrice { get; set; }
}
