using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Tests.TestHelpers;

/// <summary>
/// Mock implementation of ITickStreamService for fast testing without real network calls
/// </summary>
public class MockTickStreamService : ITickStreamService
{
    private readonly ConcurrentBag<string> _subscribedSymbols = new();
    private TickStreamStatus _status = TickStreamStatus.Stopped;

    // Events
    public event EventHandler<TradeTickEventArgs>? TradeReceived;
    public event EventHandler<QuoteTickEventArgs>? QuoteReceived;
    public event EventHandler<AggregateTickEventArgs>? AggregateReceived;
    public event EventHandler<TickStreamStatusEventArgs>? StatusChanged;

    // Properties
    public TickStreamStatus Status => _status;
    public IReadOnlyList<string> SubscribedSymbols => _subscribedSymbols.ToList();

    // Mock configuration
    public TimeSpan MockSubscriptionDelay { get; set; } = TimeSpan.FromMilliseconds(10); // Fast for tests
    public bool SimulateErrors { get; set; } = false;
    public bool SimulateSlowOperations { get; set; } = false;

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (SimulateSlowOperations)
            await Task.Delay(100, cancellationToken);
        else
            await Task.Delay(MockSubscriptionDelay, cancellationToken);

        if (SimulateErrors)
            throw new InvalidOperationException("Mock error during start");

        SetStatus(TickStreamStatus.Active, "Mock service started");
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (SimulateSlowOperations)
            await Task.Delay(50, cancellationToken);
        else
            await Task.Delay(MockSubscriptionDelay, cancellationToken);

        _subscribedSymbols.Clear();
        SetStatus(TickStreamStatus.Stopped, "Mock service stopped");
    }

    public async Task SubscribeAsync(IEnumerable<string> symbols, TickDataTypes dataTypes, CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;

        if (SimulateSlowOperations)
            await Task.Delay(1000, cancellationToken); // Simulate slow network call
        else
            await Task.Delay(MockSubscriptionDelay, cancellationToken);

        if (SimulateErrors)
            throw new InvalidOperationException($"Mock error subscribing to {symbolList.Count} symbols");

        foreach (var symbol in symbolList)
        {
            _subscribedSymbols.Add(symbol);
        }

        SetStatus(TickStreamStatus.Active, $"Subscribed to {symbolList.Count} symbols");
    }

    public async Task UnsubscribeAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;

        if (SimulateSlowOperations)
            await Task.Delay(500, cancellationToken);
        else
            await Task.Delay(MockSubscriptionDelay, cancellationToken);

        // Note: ConcurrentBag doesn't have TryRemove, but for testing this is fine
        // In a real implementation, we'd use a different collection

        SetStatus(TickStreamStatus.Active, $"Unsubscribed from {symbolList.Count} symbols");
    }

    public async Task UnsubscribeAllAsync(CancellationToken cancellationToken = default)
    {
        if (SimulateSlowOperations)
            await Task.Delay(100, cancellationToken);
        else
            await Task.Delay(MockSubscriptionDelay, cancellationToken);

        // Clear all subscriptions
        while (_subscribedSymbols.TryTake(out _)) { }

        SetStatus(TickStreamStatus.Active, "Unsubscribed from all symbols");
    }

    public async Task<TradeTick?> GetLatestTradeAsync(string symbol)
    {
        await Task.Delay(MockSubscriptionDelay);

        // Return mock latest trade
        return new TradeTick(
            symbol,
            150m + (decimal)(Random.Shared.NextDouble() * 10 - 5),
            Random.Shared.Next(100, 1000),
            DateTime.UtcNow,
            "NASDAQ",
            "Regular"
        );
    }

    public async Task<QuoteTick?> GetLatestQuoteAsync(string symbol)
    {
        await Task.Delay(MockSubscriptionDelay);

        // Return mock latest quote
        var midPrice = 150m + (decimal)(Random.Shared.NextDouble() * 10 - 5);
        return new QuoteTick(
            symbol,
            midPrice - 0.01m,
            midPrice + 0.01m,
            Random.Shared.Next(100, 500),
            Random.Shared.Next(100, 500),
            DateTime.UtcNow,
            "NASDAQ"
        );
    }

    public async Task<IReadOnlyList<TradeTick>> GetRecentTradesAsync(string symbol, int count = 100)
    {
        await Task.Delay(MockSubscriptionDelay);
        
        // Return mock trade data
        var trades = new List<TradeTick>();
        var baseTime = DateTime.UtcNow.AddMinutes(-count);
        
        for (int i = 0; i < count; i++)
        {
            trades.Add(new TradeTick(
                symbol,
                150m + (decimal)(Random.Shared.NextDouble() * 10 - 5), // 145-155 range
                Random.Shared.Next(100, 1000),
                baseTime.AddMinutes(i),
                "NASDAQ",
                "Regular"
            ));
        }
        
        return trades;
    }

    public async Task<IReadOnlyList<QuoteTick>> GetRecentQuotesAsync(string symbol, int count = 100)
    {
        await Task.Delay(MockSubscriptionDelay);
        
        // Return mock quote data
        var quotes = new List<QuoteTick>();
        var baseTime = DateTime.UtcNow.AddMinutes(-count);
        
        for (int i = 0; i < count; i++)
        {
            var midPrice = 150m + (decimal)(Random.Shared.NextDouble() * 10 - 5);
            quotes.Add(new QuoteTick(
                symbol,
                midPrice - 0.01m,
                midPrice + 0.01m,
                Random.Shared.Next(100, 500),
                Random.Shared.Next(100, 500),
                baseTime.AddMinutes(i),
                "NASDAQ"
            ));
        }
        
        return quotes;
    }

    public async Task<IEnumerable<AggregateTick>> GetRecentAggregatesAsync(string symbol, int count = 10)
    {
        await Task.Delay(MockSubscriptionDelay);
        
        // Return mock aggregate data
        var aggregates = new List<AggregateTick>();
        var baseTime = DateTime.UtcNow.AddMinutes(-count);
        
        for (int i = 0; i < count; i++)
        {
            var open = 150m + (decimal)(Random.Shared.NextDouble() * 10 - 5);
            var close = open + (decimal)(Random.Shared.NextDouble() * 2 - 1);
            
            aggregates.Add(new AggregateTick(
                symbol,
                open,
                Math.Max(open, close) + (decimal)(Random.Shared.NextDouble() * 0.5),
                Math.Min(open, close) - (decimal)(Random.Shared.NextDouble() * 0.5),
                close,
                Random.Shared.Next(10000, 100000),
                (open + close) / 2,
                baseTime.AddMinutes(i),
                Random.Shared.Next(100, 1000)
            ));
        }
        
        return aggregates;
    }

    public async Task<bool> IsAboveFiveMinuteHighAsync(string symbol)
    {
        await Task.Delay(MockSubscriptionDelay);

        // Return random result for testing
        return Random.Shared.NextDouble() > 0.5;
    }

    public async Task<decimal?> CalculateVwapAsync(string symbol, int minutes = 5)
    {
        await Task.Delay(MockSubscriptionDelay);

        // Return mock VWAP
        return 150m + (decimal)(Random.Shared.NextDouble() * 10 - 5);
    }

    public async Task<decimal?> GetBidAskSpreadAsync(string symbol)
    {
        await Task.Delay(MockSubscriptionDelay);

        // Return mock spread
        return 0.01m + (decimal)(Random.Shared.NextDouble() * 0.05);
    }

    // Helper methods for testing
    public void SimulateTradeReceived(string symbol, decimal price, int size)
    {
        var tradeTick = new TradeTick(
            symbol,
            price,
            size,
            DateTime.UtcNow,
            "NASDAQ",
            "Regular"
        );

        TradeReceived?.Invoke(this, new TradeTickEventArgs { TradeTick = tradeTick });
    }

    public void SimulateQuoteReceived(string symbol, decimal bidPrice, decimal askPrice)
    {
        var quoteTick = new QuoteTick(
            symbol,
            bidPrice,
            askPrice,
            100,
            100,
            DateTime.UtcNow,
            "NASDAQ"
        );

        QuoteReceived?.Invoke(this, new QuoteTickEventArgs { QuoteTick = quoteTick });
    }

    public void SimulateAggregateReceived(string symbol, decimal open, decimal high, decimal low, decimal close, long volume)
    {
        var aggregateTick = new AggregateTick(
            symbol,
            open,
            high,
            low,
            close,
            volume,
            (open + high + low + close) / 4,
            DateTime.UtcNow,
            100
        );

        AggregateReceived?.Invoke(this, new AggregateTickEventArgs { AggregateTick = aggregateTick });
    }

    private void SetStatus(TickStreamStatus status, string message, Exception? error = null)
    {
        _status = status;

        StatusChanged?.Invoke(this, new TickStreamStatusEventArgs
        {
            Status = status,
            Message = message,
            Exception = error
        });
    }

    public void Dispose()
    {
        while (_subscribedSymbols.TryTake(out _)) { }
        SetStatus(TickStreamStatus.Stopped, "Disposed");
    }
}
