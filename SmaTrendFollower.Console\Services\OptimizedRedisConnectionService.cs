using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for optimized Redis connection management with connection pooling and health monitoring
/// </summary>
public interface IOptimizedRedisConnectionService : IDisposable
{
    /// <summary>
    /// Gets a database instance with connection pooling
    /// </summary>
    Task<IDatabase> GetDatabaseAsync(int database = 0);

    /// <summary>
    /// Gets a database instance synchronously (for cached connections)
    /// </summary>
    IDatabase GetDatabase(int db = 0);

    /// <summary>
    /// Gets connection health status
    /// </summary>
    Task<RedisHealthStatus> GetHealthStatusAsync();

    /// <summary>
    /// Gets connection performance metrics
    /// </summary>
    RedisPerformanceMetrics GetPerformanceMetrics();

    /// <summary>
    /// Warms up the connection pool
    /// </summary>
    Task WarmupConnectionPoolAsync();
}

/// <summary>
/// Optimized Redis connection service implementation
/// </summary>
public sealed class OptimizedRedisConnectionService : IOptimizedRedisConnectionService
{
    private readonly IConnectionMultiplexer _muxer;
    private readonly IDatabase _db0;
    private readonly IConfiguration _configuration;
    private readonly ILogger<OptimizedRedisConnectionService> _logger;
    private readonly ConcurrentDictionary<int, IDatabase> _databaseCache;
    private readonly RedisPerformanceTracker _performanceTracker;
    private bool _disposed;

    public OptimizedRedisConnectionService(
        IConnectionMultiplexer muxer,
        IConfiguration configuration,
        ILogger<OptimizedRedisConnectionService> logger)
    {
        _muxer = muxer;
        _db0 = _muxer.GetDatabase(0);      // cache default DB once
        _configuration = configuration;
        _logger = logger;
        _databaseCache = new ConcurrentDictionary<int, IDatabase>();
        _performanceTracker = new RedisPerformanceTracker();

        // Subscribe to connection events on the injected multiplexer
        _muxer.ConnectionFailed += OnConnectionFailed;
        _muxer.ConnectionRestored += OnConnectionRestored;
        _muxer.InternalError += OnInternalError;
    }

    public Task<IDatabase> GetDatabaseAsync(int database = 0)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(OptimizedRedisConnectionService));

        // Return cached _db0 for database 0 to avoid redundant multiplexer
        if (database == 0)
        {
            return Task.FromResult(_db0);
        }

        // Return cached database if available
        if (_databaseCache.TryGetValue(database, out var cachedDb))
        {
            return Task.FromResult(cachedDb);
        }

        // Use the injected multiplexer instead of creating a new one
        var db = _muxer.GetDatabase(database);
        _databaseCache.TryAdd(database, db);

        _logger.LogDebug("Created database connection for database {Database}", database);
        return Task.FromResult(db);
    }

    public IDatabase GetDatabase(int db = 0)
        => db == 0 ? _db0 : _muxer.GetDatabase(db);

    public async Task<RedisHealthStatus> GetHealthStatusAsync()
    {
        try
        {
            if (_muxer == null || !_muxer.IsConnected)
            {
                return new RedisHealthStatus(false, "Not connected", null, null);
            }

            var stopwatch = Stopwatch.StartNew();
            var db = await GetDatabaseAsync();
            var pingResult = await db.PingAsync();
            stopwatch.Stop();

            var latencyMs = stopwatch.Elapsed.TotalMilliseconds;
            var isHealthy = latencyMs < 100; // Consider healthy if ping < 100ms

            return new RedisHealthStatus(
                isHealthy,
                isHealthy ? "Healthy" : $"High latency: {latencyMs:F1}ms",
                latencyMs,
                null); // GetCounters() returns ServerCounters, not ConnectionCounters
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Redis health check failed");
            return new RedisHealthStatus(false, ex.Message, null, null);
        }
    }

    public RedisPerformanceMetrics GetPerformanceMetrics()
    {
        return _performanceTracker.GetMetrics();
    }

    public async Task WarmupConnectionPoolAsync()
    {
        try
        {
            _logger.LogInformation("Warming up Redis connection pool");

            // Pre-create connections for common databases
            var databases = new[] { 0, 1, 2 }; // Common database numbers
            var warmupTasks = databases.Select(async db =>
            {
                try
                {
                    var database = await GetDatabaseAsync(db);
                    await database.PingAsync();
                    _logger.LogDebug("Warmed up connection to database {Database}", db);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to warm up connection to database {Database}", db);
                }
            });

            await Task.WhenAll(warmupTasks);
            _logger.LogInformation("Redis connection pool warmup completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis connection pool warmup failed");
        }
    }





    private void OnConnectionFailed(object? sender, ConnectionFailedEventArgs e)
    {
        _logger.LogWarning("Redis connection failed: {Exception}", e.Exception?.Message);
        _performanceTracker.RecordConnectionFailure();
    }

    private void OnConnectionRestored(object? sender, ConnectionFailedEventArgs e)
    {
        _logger.LogInformation("Redis connection restored");
        _performanceTracker.RecordConnectionRestored();
    }

    private void OnInternalError(object? sender, InternalErrorEventArgs e)
    {
        _logger.LogError(e.Exception, "Redis internal error: {Origin}", e.Origin);
        _performanceTracker.RecordInternalError();
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            // Unsubscribe from events on the injected multiplexer
            _muxer.ConnectionFailed -= OnConnectionFailed;
            _muxer.ConnectionRestored -= OnConnectionRestored;
            _muxer.InternalError -= OnInternalError;

            _logger.LogInformation("OptimizedRedisConnectionService disposed");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disposing OptimizedRedisConnectionService");
        }
    }
}

/// <summary>
/// Redis health status information
/// </summary>
public readonly record struct RedisHealthStatus(
    bool IsHealthy,
    string Status,
    double? LatencyMs,
    ConnectionCounters? Counters
);

/// <summary>
/// Redis performance metrics
/// </summary>
public readonly record struct RedisPerformanceMetrics(
    int TotalConnections,
    int FailedConnections,
    int RestoredConnections,
    int InternalErrors,
    double AverageLatencyMs,
    DateTime LastConnectionTime
);

/// <summary>
/// Performance tracker for Redis operations
/// </summary>
internal class RedisPerformanceTracker
{
    private int _totalConnections;
    private int _failedConnections;
    private int _restoredConnections;
    private int _internalErrors;
    private readonly List<double> _latencies = new();
    private DateTime _lastConnectionTime = DateTime.UtcNow;

    public void RecordConnection()
    {
        Interlocked.Increment(ref _totalConnections);
    }

    public void RecordConnectionFailure()
    {
        Interlocked.Increment(ref _failedConnections);
    }

    public void RecordConnectionRestored()
    {
        Interlocked.Increment(ref _restoredConnections);
        _lastConnectionTime = DateTime.UtcNow;
    }

    public void RecordInternalError()
    {
        Interlocked.Increment(ref _internalErrors);
    }

    public RedisPerformanceMetrics GetMetrics()
    {
        var averageLatency = _latencies.Count > 0 ? _latencies.Average() : 0.0;
        
        return new RedisPerformanceMetrics(
            _totalConnections,
            _failedConnections,
            _restoredConnections,
            _internalErrors,
            averageLatency,
            _lastConnectionTime
        );
    }
}
