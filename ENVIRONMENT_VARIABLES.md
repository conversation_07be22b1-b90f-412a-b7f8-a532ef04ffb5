# SmaTrendFollower Environment Variables

## Canonical Environment Variable Set

Use these **single, canonical environment variables** for each service. No duplicates, no confusion.

### **Market Data APIs**
```bash
POLYGON_API_KEY=********************************
```

### **Trading APIs**
```bash
ALPACA_KEY_ID=AKGBPW5HD8LVI5C6NJUJ
ALPACA_SECRET=MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM
```
*Note: Same credentials used for both Alpaca trading and Alpaca news services*

### **AI Services**
```bash
GEMINI_API_KEY=AIzaSyCh_cCKoG5cY7WeSIel65-G7HbzDQQy1qc
OPENAI_API_KEY=[Set from secure storage]
```

### **Communication**
```bash
DISCORD_BOT_TOKEN=[Set from secure storage]
DISCORD_CHANNEL_ID=1385057459814797383
```

### **Infrastructure**
```bash
REDIS_URL=192.168.1.168:6379
```

## Configuration Mapping

The application maps these environment variables to configuration sections:

| Environment Variable | Configuration Section | Used By |
|---------------------|----------------------|---------|
| `POLYGON_API_KEY` | `Polygon:ApiKey` | Market data |
| `ALPACA_KEY_ID` | `Alpaca:KeyId` + `AlpacaNews:KeyId` | Trading + News |
| `ALPACA_SECRET` | `Alpaca:SecretKey` + `AlpacaNews:Secret` | Trading + News |
| `GEMINI_API_KEY` | `Gemini:ApiKey` | Sentiment analysis |
| `OPENAI_API_KEY` | `OpenAI:ApiKey` | AI services |
| `DISCORD_BOT_TOKEN` | `Discord:BotToken` | Notifications |
| `DISCORD_CHANNEL_ID` | `Discord:ChannelId` | Notifications |
| `REDIS_URL` | `Redis:ConnectionString` | Caching |

## Environment Setup

### PowerShell (Windows)
```powershell
$env:POLYGON_API_KEY="********************************"
$env:ALPACA_KEY_ID="AKGBPW5HD8LVI5C6NJUJ"
$env:ALPACA_SECRET="MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM"
$env:GEMINI_API_KEY="AIzaSyCh_cCKoG5cY7WeSIel65-G7HbzDQQy1qc"
$env:DISCORD_CHANNEL_ID="1385057459814797383"
$env:REDIS_URL="192.168.1.168:6379"
```

### Bash (Linux/Mac)
```bash
export POLYGON_API_KEY="********************************"
export ALPACA_KEY_ID="AKGBPW5HD8LVI5C6NJUJ"
export ALPACA_SECRET="MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM"
export GEMINI_API_KEY="AIzaSyCh_cCKoG5cY7WeSIel65-G7HbzDQQy1qc"
export DISCORD_CHANNEL_ID="1385057459814797383"
export REDIS_URL="192.168.1.168:6379"
```

## Verification

The application logs will show all 10 secrets loaded successfully:
```
🔐 Loaded 10 secrets from Environment Variables
🔑 Mapped secret: Polygon:ApiKey = ✅ Set
🔑 Mapped secret: Alpaca:KeyId = ✅ Set
🔑 Mapped secret: AlpacaNews:KeyId = ✅ Set
🔑 Mapped secret: Alpaca:SecretKey = ✅ Set
🔑 Mapped secret: AlpacaNews:Secret = ✅ Set
🔑 Mapped secret: OpenAI:ApiKey = ✅ Set
🔑 Mapped secret: Discord:BotToken = ✅ Set
🔑 Mapped secret: Discord:ChannelId = ✅ Set
🔑 Mapped secret: Gemini:ApiKey = ✅ Set
🔑 Mapped secret: Redis:ConnectionString = ✅ Set
```

## Notes

- **No duplicate variables**: Each service uses exactly one environment variable
- **Shared credentials**: Alpaca trading and news use the same credentials (as they should)
- **Clean mapping**: Simple 1:1 or 1:2 mapping from environment variables to configuration
- **Production ready**: All APIs tested and working with these consolidated variables
