using Microsoft.ML;
using Microsoft.ML.Data;
using System;
using System.IO;

public class RegimeTrainingRow
{
    [LoadColumn(0)]
    public string Date { get; set; } = string.Empty;

    [LoadColumn(1)]
    public float SPX_Ret { get; set; }

    [LoadColumn(2)]
    public float VIX_Level { get; set; }

    [LoadColumn(3)]
    public float VIX_Change { get; set; }

    [LoadColumn(4)]
    public float Breadth_Score { get; set; }

    [LoadColumn(5)]
    public uint RegimeLabel { get; set; }
}

class Program
{
    static void Main()
    {
        var mlContext = new MLContext(seed: 42);
        var csvPath = "Model/regime.csv";
        
        Console.WriteLine($"Checking file: {csvPath}");
        Console.WriteLine($"File exists: {File.Exists(csvPath)}");
        
        if (File.Exists(csvPath))
        {
            Console.WriteLine("First few lines of file:");
            var lines = File.ReadAllLines(csvPath);
            for (int i = 0; i < Math.Min(5, lines.Length); i++)
            {
                Console.WriteLine($"Line {i}: {lines[i]}");
            }
            
            try
            {
                var dataView = mlContext.Data.LoadFromTextFile<RegimeTrainingRow>(
                    csvPath, 
                    hasHeader: true, 
                    separatorChar: ',');

                var dataCount = dataView.GetRowCount() ?? 0;
                Console.WriteLine($"Loaded {dataCount} training samples");
                
                // Try to enumerate the data
                var data = mlContext.Data.CreateEnumerable<RegimeTrainingRow>(dataView, reuseRowObject: false).Take(3);
                Console.WriteLine("Sample data:");
                foreach (var row in data)
                {
                    Console.WriteLine($"Date: {row.Date}, SPX_Ret: {row.SPX_Ret}, VIX_Level: {row.VIX_Level}, VIX_Change: {row.VIX_Change}, Breadth_Score: {row.Breadth_Score}, RegimeLabel: {row.RegimeLabel}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading CSV: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
