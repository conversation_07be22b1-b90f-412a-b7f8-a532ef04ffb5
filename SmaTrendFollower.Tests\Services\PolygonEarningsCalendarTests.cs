using System;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class PolygonEarningsCalendarTests : IDisposable
{
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<PolygonEarningsCalendar>> _mockLogger;
    private readonly IMemoryCache _memoryCache;
    private readonly Mock<HttpMessageHandler> _mockHttpHandler;
    private readonly HttpClient _httpClient;

    public PolygonEarningsCalendarTests()
    {
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<PolygonEarningsCalendar>>();
        _memoryCache = new MemoryCache(new MemoryCacheOptions());
        _mockHttpHandler = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_mockHttpHandler.Object);

        // Set up environment variable for API key
        Environment.SetEnvironmentVariable("POLY_API_KEY", "test-api-key");
    }

    [Fact]
    public async Task GetNextEarningsAsync_WithValidResponse_ReturnsNextEarningsDate()
    {
        // Arrange
        var symbol = "AAPL";
        var futureDate = DateTime.UtcNow.AddDays(5);
        var responseJson = JsonSerializer.Serialize(new
        {
            results = new[]
            {
                new { reportTime = futureDate.ToString("yyyy-MM-ddTHH:mm:ssZ") }
            }
        });

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson)
            });

        var service = new PolygonEarningsCalendar(_httpClient, _mockConfiguration.Object, _memoryCache, _mockLogger.Object);

        // Act
        var result = await service.GetNextEarningsAsync(symbol);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeCloseTo(futureDate, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public async Task GetNextEarningsAsync_WithEmptyResults_ReturnsNull()
    {
        // Arrange
        var symbol = "UNKNOWN";
        var responseJson = JsonSerializer.Serialize(new { results = new object[0] });

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson)
            });

        var service = new PolygonEarningsCalendar(_httpClient, _mockConfiguration.Object, _memoryCache, _mockLogger.Object);

        // Act
        var result = await service.GetNextEarningsAsync(symbol);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetNextEarningsAsync_WithPastEarnings_ReturnsNull()
    {
        // Arrange
        var symbol = "PAST";
        var pastDate = DateTime.UtcNow.AddDays(-5);
        var responseJson = JsonSerializer.Serialize(new
        {
            results = new[]
            {
                new { reportTime = pastDate.ToString("yyyy-MM-ddTHH:mm:ssZ") }
            }
        });

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson)
            });

        var service = new PolygonEarningsCalendar(_httpClient, _mockConfiguration.Object, _memoryCache, _mockLogger.Object);

        // Act
        var result = await service.GetNextEarningsAsync(symbol);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetNextEarningsAsync_WithHttpError_ReturnsNull()
    {
        // Arrange
        var symbol = "ERROR";

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError
            });

        var service = new PolygonEarningsCalendar(_httpClient, _mockConfiguration.Object, _memoryCache, _mockLogger.Object);

        // Act
        var result = await service.GetNextEarningsAsync(symbol);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetNextEarningsAsync_CachesResults()
    {
        // Arrange
        var symbol = "CACHED";
        var futureDate = DateTime.UtcNow.AddDays(3);
        var responseJson = JsonSerializer.Serialize(new
        {
            results = new[]
            {
                new { reportTime = futureDate.ToString("yyyy-MM-ddTHH:mm:ssZ") }
            }
        });

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson)
            });

        var service = new PolygonEarningsCalendar(_httpClient, _mockConfiguration.Object, _memoryCache, _mockLogger.Object);

        // Act - First call
        var result1 = await service.GetNextEarningsAsync(symbol);
        
        // Act - Second call (should use cache)
        var result2 = await service.GetNextEarningsAsync(symbol);

        // Assert
        result1.Should().NotBeNull();
        result2.Should().NotBeNull();
        result1.Should().Be(result2);

        // Verify HTTP was only called once
        _mockHttpHandler.Protected()
            .Verify("SendAsync", Times.Once(), ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>());
    }

    [Fact]
    public async Task GetNextEarningsAsync_WithEmptySymbol_ReturnsNull()
    {
        // Arrange
        var service = new PolygonEarningsCalendar(_httpClient, _mockConfiguration.Object, _memoryCache, _mockLogger.Object);

        // Act
        var result = await service.GetNextEarningsAsync("");

        // Assert
        result.Should().BeNull();
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
        _memoryCache?.Dispose();
        Environment.SetEnvironmentVariable("POLY_API_KEY", null);
    }
}
