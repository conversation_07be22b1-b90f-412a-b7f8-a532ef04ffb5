using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;
using Xunit.Abstractions;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for PolygonWebSocketManager
/// Tests real Redis integration, large symbol universe handling, and reconnection scenarios
/// </summary>
[Collection("Integration")]
public class PolygonWebSocketManagerIntegrationTests : IAsyncDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly ServiceProvider _serviceProvider;
    private readonly PolygonWebSocketManager _manager;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IDatabase _database;
    private readonly ILogger<PolygonWebSocketManagerIntegrationTests> _logger;

    public PolygonWebSocketManagerIntegrationTests(ITestOutputHelper output)
    {
        _output = output;

        // Setup test configuration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["REDIS_URL"] = "*************:6379",
                ["REDIS_DATABASE"] = "0",
                ["POLY_API_KEY"] = "test-api-key-for-integration-tests"
            })
            .Build();

        // Setup DI container
        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var connectionString = configuration.GetConnectionString("Redis") ?? "*************:6379";
            return ConnectionMultiplexer.Connect(connectionString);
        });
        services.AddSingleton<IOptimizedRedisConnectionService>(provider =>
        {
            var muxer = provider.GetRequiredService<IConnectionMultiplexer>();
            var config = provider.GetRequiredService<IConfiguration>();
            var logger = provider.GetRequiredService<ILogger<OptimizedRedisConnectionService>>();
            return new OptimizedRedisConnectionService(muxer, config, logger);
        });
        services.AddSingleton<PolygonWebSocketManager>(provider =>
        {
            var redisService = provider.GetRequiredService<IOptimizedRedisConnectionService>();
            var logger = provider.GetRequiredService<ILogger<PolygonWebSocketManager>>();
            var apiKey = configuration["POLY_API_KEY"]!;
            
            return new PolygonWebSocketManager(redisService, logger, apiKey);
        });

        _serviceProvider = services.BuildServiceProvider();
        _manager = _serviceProvider.GetRequiredService<PolygonWebSocketManager>();
        _redisService = _serviceProvider.GetRequiredService<IOptimizedRedisConnectionService>();
        _logger = _serviceProvider.GetRequiredService<ILogger<PolygonWebSocketManagerIntegrationTests>>();
        
        // Get Redis database for direct testing
        _database = _redisService.GetDatabaseAsync().GetAwaiter().GetResult();
    }

    [Fact]
    public async Task RedisConnection_ShouldBeEstablished()
    {
        // Act
        var database = await _redisService.GetDatabaseAsync();
        var testKey = "test:websocket:integration";
        var testValue = "test-value";

        await database.StringSetAsync(testKey, testValue, TimeSpan.FromMinutes(1));
        var retrievedValue = await database.StringGetAsync(testKey);

        // Assert
        retrievedValue.Should().Be(testValue);

        // Cleanup
        await database.KeyDeleteAsync(testKey);
    }

    [Fact]
    public async Task BatchSubscribeAsync_WithLargeUniverse_ShouldPersistToRedis()
    {
        // Arrange
        var symbols = GenerateTestSymbols(250);
        var channel = PolygonWsChannel.EquityMinute;
        var redisKey = channel.GetRedisSubscriptionKey();

        // Cleanup any existing data
        await _database.KeyDeleteAsync(redisKey);

        // Act
        try
        {
            await _manager.BatchSubscribeAsync(channel, symbols);
        }
        catch (Exception ex)
        {
            // WebSocket connection will fail in test environment, but Redis persistence should work
            _output.WriteLine($"Expected WebSocket connection failure: {ex.Message}");
        }

        // Assert
        var persistedData = await _database.StringGetAsync(redisKey);
        persistedData.Should().NotBeNull();
        
        var persistedSymbols = persistedData.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries);
        persistedSymbols.Should().HaveCount(250);
        persistedSymbols.Should().BeEquivalentTo(symbols);

        // Verify TTL is set
        var ttl = await _database.KeyTimeToLiveAsync(redisKey);
        ttl.Should().NotBeNull();
        ttl.Should().BeCloseTo(RedisKeyConstants.RedisKeyTTL.WebSocketSubscriptions, TimeSpan.FromMinutes(1));

        // Cleanup
        await _database.KeyDeleteAsync(redisKey);
    }

    [Fact]
    public async Task BatchSubscribeAsync_WithMultipleChannels_ShouldPersistSeparately()
    {
        // Arrange
        var equitySymbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var indexSymbols = new[] { "VIX", "SPX" };

        var equityChannel = PolygonWsChannel.EquityTrades;
        var indexChannel = PolygonWsChannel.IndexMinute;

        var equityRedisKey = equityChannel.GetRedisSubscriptionKey();
        var indexRedisKey = indexChannel.GetRedisSubscriptionKey();

        // Cleanup any existing data
        await _database.KeyDeleteAsync(equityRedisKey);
        await _database.KeyDeleteAsync(indexRedisKey);

        // Act
        try
        {
            await _manager.BatchSubscribeAsync(equityChannel, equitySymbols);
            await _manager.BatchSubscribeAsync(indexChannel, indexSymbols);
        }
        catch (Exception ex)
        {
            _output.WriteLine($"Expected WebSocket connection failure: {ex.Message}");
        }

        // Assert
        var equityData = await _database.StringGetAsync(equityRedisKey);
        var indexData = await _database.StringGetAsync(indexRedisKey);

        equityData.Should().NotBeNull();
        indexData.Should().NotBeNull();

        var persistedEquitySymbols = equityData.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries);
        var persistedIndexSymbols = indexData.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries);

        persistedEquitySymbols.Should().BeEquivalentTo(equitySymbols);
        persistedIndexSymbols.Should().BeEquivalentTo(indexSymbols);

        // Cleanup
        await _database.KeyDeleteAsync(equityRedisKey);
        await _database.KeyDeleteAsync(indexRedisKey);
    }

    [Fact]
    public async Task BatchSubscribeAsync_WithChunking_ShouldHandleLargeSymbolLists()
    {
        // Arrange
        var symbols = GenerateTestSymbols(1250); // Should create 3 chunks of 500, 500, 250
        var channel = PolygonWsChannel.EquityQuotes;
        var redisKey = channel.GetRedisSubscriptionKey();

        // Cleanup any existing data
        await _database.KeyDeleteAsync(redisKey);

        // Act
        try
        {
            await _manager.BatchSubscribeAsync(channel, symbols);
        }
        catch (Exception ex)
        {
            _output.WriteLine($"Expected WebSocket connection failure: {ex.Message}");
        }

        // Assert
        var persistedData = await _database.StringGetAsync(redisKey);
        persistedData.Should().NotBeNull();
        
        var persistedSymbols = persistedData.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries);
        persistedSymbols.Should().HaveCount(1250);
        persistedSymbols.Should().BeEquivalentTo(symbols);

        // Cleanup
        await _database.KeyDeleteAsync(redisKey);
    }

    [Fact]
    public async Task RedisKeyTTL_ShouldMatchConstants()
    {
        // Arrange
        var symbols = new[] { "TEST1", "TEST2" };
        var channel = PolygonWsChannel.EquityMinute;
        var redisKey = channel.GetRedisSubscriptionKey();

        // Cleanup any existing data
        await _database.KeyDeleteAsync(redisKey);

        // Act
        try
        {
            await _manager.BatchSubscribeAsync(channel, symbols);
        }
        catch (Exception ex)
        {
            _output.WriteLine($"Expected WebSocket connection failure: {ex.Message}");
        }

        // Assert
        var ttl = await _database.KeyTimeToLiveAsync(redisKey);
        ttl.Should().NotBeNull();
        ttl.Should().BeCloseTo(RedisKeyConstants.RedisKeyTTL.WebSocketSubscriptions, TimeSpan.FromMinutes(1));

        // Cleanup
        await _database.KeyDeleteAsync(redisKey);
    }

    [Fact]
    public void GetConnectionState_WithNoConnection_ShouldReturnNone()
    {
        // Act
        var state = _manager.GetConnectionState(PolygonWsChannel.EquityTrades);

        // Assert
        state.Should().Be(System.Net.WebSockets.WebSocketState.None);
    }

    [Fact]
    public void GetSubscribedSymbolCount_WithNoConnection_ShouldReturnZero()
    {
        // Act
        var count = _manager.GetSubscribedSymbolCount(PolygonWsChannel.EquityTrades);

        // Assert
        count.Should().Be(0);
    }

    private static List<string> GenerateTestSymbols(int count)
    {
        return Enumerable.Range(1, count)
            .Select(i => $"TEST{i:D4}")
            .ToList();
    }

    public async ValueTask DisposeAsync()
    {
        _manager?.Dispose();
        _serviceProvider?.Dispose();
        await Task.CompletedTask;
    }
}
