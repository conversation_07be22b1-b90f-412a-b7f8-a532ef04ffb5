using System.Collections.Concurrent;
using StackExchange.Redis;
using Prometheus;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time quote volatility guard that monitors bid-ask spreads for unusual volatility.
/// Automatically halts trading for symbols showing excessive spread volatility (z-score > 2.0)
/// to prevent execution during market disruptions or quote instability.
///
/// Uses a time-based rolling 2-minute window to detect spread anomalies with 2-sigma threshold,
/// which is more sensitive than the 3-sigma threshold used by AnomalyDetector for price returns.
/// Time-based windowing handles variable tick frequencies better than count-based windows.
/// </summary>
public sealed class QuoteVolatilityGuard : IDisposable
{
    private readonly IDatabase? _redis;
    private readonly ILogger<QuoteVolatilityGuard> _logger;
    private readonly ConcurrentDictionary<string, TimeBasedRollingStats> _spreadStats = new();
    private bool _disposed;

    // Configuration constants - Changed to time-based windowing
    private static readonly TimeSpan WindowDuration = TimeSpan.FromMinutes(2);  // 2-minute rolling window
    private const double ThresholdMultiplier = 2.0;   // 2σ vs 3σ in AnomalyDetector
    private const int HaltDurationMinutes = 2;        // 2-minute halt duration

    // Prometheus metrics
    private static readonly Counter QuoteVolatilityHalts =
        Metrics.CreateCounter("quote_vol_halts_total", "Total quote volatility halts",
            new CounterConfiguration { LabelNames = new[] { "symbol" } });

    private static readonly Gauge QuoteVolatilityHalted =
        Metrics.CreateGauge("quote_vol_halted", "Quote volatility halt active for symbol",
            new GaugeConfiguration { LabelNames = new[] { "symbol" } });

    private static readonly Histogram SpreadZScores =
        Metrics.CreateHistogram("spread_z_scores", "Distribution of spread z-scores",
            new HistogramConfiguration 
            { 
                LabelNames = new[] { "symbol" },
                Buckets = new[] { 0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0, 5.0 }
            });

    private static readonly Counter SpreadChecks =
        Metrics.CreateCounter("spread_checks_total", "Total spread volatility checks performed",
            new CounterConfiguration { LabelNames = new[] { "symbol" } });

    public QuoteVolatilityGuard(
        IConnectionMultiplexer? connectionMultiplexer,
        ILogger<QuoteVolatilityGuard> logger)
    {
        _redis = connectionMultiplexer?.GetDatabase(); // Allow null Redis for degraded mode
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        if (_redis == null)
        {
            _logger.LogWarning("Redis not available, QuoteVolatilityGuard will operate in degraded mode (in-memory only)");
        }

        _logger.LogInformation("QuoteVolatilityGuard initialized with window={WindowDuration}, threshold={Threshold}σ, halt={HaltDuration}m",
            WindowDuration, ThresholdMultiplier, HaltDurationMinutes);
    }

    /// <summary>
    /// Processes a quote update and checks for spread volatility anomalies
    /// </summary>
    /// <param name="symbol">Trading symbol</param>
    /// <param name="bid">Bid price</param>
    /// <param name="ask">Ask price</param>
    public void OnQuote(string symbol, decimal bid, decimal ask)
    {
        if (string.IsNullOrEmpty(symbol) || bid <= 0 || ask <= 0 || ask <= bid)
        {
            return; // Skip invalid quotes
        }

        try
        {
            // Calculate spread percentage relative to mid-price
            var midPrice = (ask + bid) / 2;
            var spreadPct = (double)((ask - bid) / midPrice);

            // Get or create time-based rolling stats for this symbol
            var stats = _spreadStats.GetOrAdd(symbol, _ => new TimeBasedRollingStats(WindowDuration));
            stats.Add(spreadPct, DateTime.UtcNow);

            // Increment check counter
            SpreadChecks.WithLabels(symbol).Inc();

            // Skip analysis if insufficient data
            if (stats.StandardDeviation == 0 || !stats.HasSufficientData)
            {
                return;
            }

            // Calculate z-score for current spread
            var zScore = Math.Abs(stats.CalculateZScore(spreadPct));
            
            // Record z-score distribution
            SpreadZScores.WithLabels(symbol).Observe(zScore);

            // Check for volatility anomaly
            if (zScore > ThresholdMultiplier)
            {
                _ = Task.Run(async () => await HaltTradingAsync(symbol, zScore, spreadPct));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing quote volatility for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Checks if trading is currently halted for a symbol due to quote volatility
    /// </summary>
    /// <param name="symbol">Trading symbol to check</param>
    /// <returns>True if trading is halted due to quote volatility, false otherwise</returns>
    public async Task<bool> IsTradingHaltedAsync(string symbol)
    {
        if (string.IsNullOrEmpty(symbol))
            return false;

        try
        {
            if (_redis == null)
            {
                return false; // No Redis available, allow trading
            }

            var haltKey = $"halt:{symbol}";
            var haltValue = await _redis.StringGetAsync(haltKey);
            return haltValue.HasValue && haltValue == "volatility";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking quote volatility halt status for {Symbol}", symbol);
            return false; // Default to allowing trading if Redis is unavailable
        }
    }

    /// <summary>
    /// Gets current spread statistics for a symbol
    /// </summary>
    /// <param name="symbol">Trading symbol</param>
    /// <returns>Spread statistics snapshot or null if no data</returns>
    public TimeBasedRollingStatsSnapshot? GetSpreadStats(string symbol)
    {
        if (string.IsNullOrEmpty(symbol))
            return null;

        return _spreadStats.TryGetValue(symbol, out var stats) ? stats.GetSnapshot() : null;
    }

    /// <summary>
    /// Clears spread statistics for a symbol (useful for testing)
    /// </summary>
    /// <param name="symbol">Trading symbol</param>
    public void ClearSpreadStats(string symbol)
    {
        if (!string.IsNullOrEmpty(symbol) && _spreadStats.TryRemove(symbol, out _))
        {
            _logger.LogDebug("Cleared spread statistics for {Symbol}", symbol);
        }
    }

    /// <summary>
    /// Halts trading for a symbol due to quote volatility anomaly
    /// </summary>
    private async Task HaltTradingAsync(string symbol, double zScore, double spreadPct)
    {
        try
        {
            if (_redis != null)
            {
                var haltKey = $"halt:{symbol}";
                await _redis.StringSetAsync(haltKey, "volatility", TimeSpan.FromMinutes(HaltDurationMinutes));
            }

            // Update metrics
            QuoteVolatilityHalted.WithLabels(symbol).Set(1);
            QuoteVolatilityHalts.WithLabels(symbol).Inc();

            _logger.LogWarning(
                "Quote volatility anomaly detected: {Symbol}, z-score={ZScore:F2}, spread={SpreadPct:F4}% - trading halted for {Duration}m",
                symbol, zScore, spreadPct * 100, HaltDurationMinutes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to halt trading for {Symbol} after quote volatility anomaly", symbol);
        }
    }

    /// <summary>
    /// Cleanup expired statistics to prevent memory leaks
    /// </summary>
    private void CleanupExpiredStats()
    {
        var cutoff = DateTime.UtcNow.AddMinutes(-30); // Keep stats for 30 minutes
        var keysToRemove = new List<string>();

        foreach (var kvp in _spreadStats)
        {
            var stats = kvp.Value;
            if (stats.Count == 0) // No recent activity
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        foreach (var key in keysToRemove)
        {
            _spreadStats.TryRemove(key, out _);
        }

        if (keysToRemove.Count > 0)
        {
            _logger.LogDebug("Cleaned up {Count} expired spread statistics", keysToRemove.Count);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _spreadStats.Clear();
        
        _logger.LogInformation("QuoteVolatilityGuard disposed");
    }
}
