using Alpaca.Markets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using StackExchange.Redis;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that processes real-time news from Alpaca and analyzes sentiment using Gemini AI
/// </summary>
public sealed class NewsSentimentService : BackgroundService
{
    private readonly IAlpacaDataStreamingClient _newsStreamingClient;
    private readonly IGeminiClient _geminiClient;
    private readonly IDatabase _redisDatabase;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<NewsSentimentService> _logger;
    private readonly SemaphoreSlim _processingLimiter;
    private readonly string _alpacaKeyId;
    private readonly string _alpacaSecret;

    public NewsSentimentService(
        IAlpacaClientFactory alpacaClientFactory,
        IGeminiClient geminiClient,
        IOptimizedRedisConnectionService redisConnectionService,
        IUniverseProvider universeProvider,
        IConfiguration configuration,
        ILogger<NewsSentimentService> logger)
    {
        _geminiClient = geminiClient;
        _universeProvider = universeProvider;
        _logger = logger;

        // Get Redis database
        _redisDatabase = redisConnectionService.GetDatabaseAsync().GetAwaiter().GetResult();

        // Get Alpaca credentials from configuration or environment
        _alpacaKeyId = configuration["AlpacaNews:KeyId"]!;
        _alpacaSecret = configuration["AlpacaNews:Secret"]!;

        if (string.IsNullOrEmpty(_alpacaKeyId))
        {
            var keyIdEnv = configuration["AlpacaNews:KeyIdEnv"] ?? "APCA_API_KEY_ID";
            _alpacaKeyId = Environment.GetEnvironmentVariable(keyIdEnv)
                          ?? throw new InvalidOperationException($"Environment variable {keyIdEnv} not found and AlpacaNews:KeyId not configured");
        }

        if (string.IsNullOrEmpty(_alpacaSecret))
        {
            var secretEnv = configuration["AlpacaNews:SecretEnv"] ?? "APCA_API_SECRET";
            _alpacaSecret = Environment.GetEnvironmentVariable(secretEnv)
                           ?? throw new InvalidOperationException($"Environment variable {secretEnv} not found and AlpacaNews:Secret not configured");
        }

        // Create Alpaca news streaming client
        _newsStreamingClient = alpacaClientFactory.CreateDataStreamingClient();

        // Limit concurrent sentiment analysis requests to avoid overwhelming Gemini API
        _processingLimiter = new SemaphoreSlim(5, 5);

        _logger.LogInformation("NewsSentimentService initialized");
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Starting news sentiment analysis service...");

            // Connect and authenticate to Alpaca news stream
            await ConnectToNewsStreamAsync(stoppingToken).ConfigureAwait(false);

            // Subscribe to news updates
            await SubscribeToNewsAsync(stoppingToken).ConfigureAwait(false);

            // Process news updates
            await ProcessNewsUpdatesAsync(stoppingToken).ConfigureAwait(false);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("News sentiment service was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in news sentiment service");
            throw;
        }
    }

    private async Task ConnectToNewsStreamAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Connecting to Alpaca news stream...");
            await _newsStreamingClient.ConnectAndAuthenticateAsync(cancellationToken)
                                          .ConfigureAwait(false);
            _logger.LogInformation("Successfully connected to Alpaca news stream");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to Alpaca news stream");
            throw;
        }
    }

    private async Task SubscribeToNewsAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Get current universe symbols for cost-gating
            var universeSymbolsList = await _universeProvider.GetSymbolsAsync()
                                                             .ConfigureAwait(false);
            var universeSymbols = universeSymbolsList?.ToHashSet() ?? new HashSet<string>();
            _logger.LogInformation("Subscribing to news for {Count} universe symbols", universeSymbols.Count);

            // TODO: Implement actual Alpaca news subscription when API is available
            // For now, we'll simulate the subscription
            _logger.LogInformation("News subscription placeholder - actual implementation pending");

            _logger.LogInformation("Successfully subscribed to Alpaca news stream");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to news stream");
            throw;
        }
    }

    private async Task ProcessNewsUpdatesAsync(CancellationToken cancellationToken)
    {
        try
        {
            // TODO: Implement actual news processing when Alpaca news API is available
            // For now, we'll simulate the processing loop
            _logger.LogInformation("News processing loop started (placeholder implementation)");

            // Keep the service running but don't process actual news yet
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromMinutes(1), cancellationToken)
                          .ConfigureAwait(false);
                _logger.LogDebug("News processing heartbeat - waiting for actual implementation");
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("News processing was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing news updates");
            throw;
        }
    }

    // TODO: Implement when Alpaca news API is available
    /*private async Task ProcessSingleNewsUpdateAsync(INewsArticle newsUpdate, CancellationToken cancellationToken)
    {
        try
        {
            // Cost-gate: Only process news for symbols in today's universe
            var universeSymbolsList = await _universeProvider.GetSymbolsAsync()
                                                             .ConfigureAwait(false);
            var universeSymbols = universeSymbolsList?.ToHashSet() ?? new HashSet<string>();
            
            // Check if any of the news symbols are in our universe
            var relevantSymbols = newsUpdate.Symbols?.Where(s => universeSymbols.Contains(s)).ToList() ?? new List<string>();
            
            if (!relevantSymbols.Any())
            {
                _logger.LogDebug("Skipping news for symbols not in universe: {Symbols}", 
                    string.Join(", ", newsUpdate.Symbols ?? new List<string>()));
                return;
            }

            // Limit concurrent processing
            await _processingLimiter.WaitAsync(cancellationToken).ConfigureAwait(false);
            
            try
            {
                // Get sentiment score from Gemini
                var sentimentScore = await _geminiClient.GetSentimentAsync(newsUpdate.Headline, cancellationToken)
                                                        .ConfigureAwait(false);
                
                if (sentimentScore == null)
                {
                    _logger.LogWarning("Failed to get sentiment score for headline: {Headline}", newsUpdate.Headline);
                    return;
                }

                // Store sentiment for each relevant symbol
                var today = DateTime.UtcNow.ToString("yyyyMMdd");
                
                foreach (var symbol in relevantSymbols)
                {
                    var redisKey = $"Sentiment:{symbol}:{today}";
                    
                    // Store sentiment score with news ID as field
                    await _redisDatabase.HashSetAsync(redisKey, newsUpdate.Id.ToString(), sentimentScore.Value)
                                        .ConfigureAwait(false);

                    // Set expiration to 24 hours
                    await _redisDatabase.KeyExpireAsync(redisKey, TimeSpan.FromHours(24))
                                        .ConfigureAwait(false);
                    
                    _logger.LogInformation("Stored sentiment for {Symbol}: {Score:F3} (News: {NewsId})", 
                        symbol, sentimentScore.Value, newsUpdate.Id);
                }

                // Also store a "latest" entry for quick access
                foreach (var symbol in relevantSymbols)
                {
                    var latestKey = $"Sentiment:{symbol}:{today}";
                    await _redisDatabase.HashSetAsync(latestKey, "latest", sentimentScore.Value)
                                        .ConfigureAwait(false);
                }
            }
            finally
            {
                _processingLimiter.Release();
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("News processing was cancelled for news ID: {NewsId}", newsUpdate.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing news update {NewsId}: {Headline}",
                newsUpdate.Id, newsUpdate.Headline);
        }
    }*/

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping news sentiment service...");
        
        try
        {
            await _newsStreamingClient.DisconnectAsync(cancellationToken)
                                          .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disconnecting from news stream");
        }

        await base.StopAsync(cancellationToken).ConfigureAwait(false);
        _logger.LogInformation("News sentiment service stopped");
    }

    public override void Dispose()
    {
        _processingLimiter?.Dispose();
        _newsStreamingClient?.Dispose();
        base.Dispose();
    }
}
